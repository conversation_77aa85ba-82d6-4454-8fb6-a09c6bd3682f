{% extends 'base.html' %}
{% load static %}
{% load meal_filters %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Kitchen Reports</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Reports</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Date Range</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'kitchen_reports' %}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Start Date</label>
                                    <input type="date" name="start_date" class="form-control" value="{{ start_date|date:'Y-m-d' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>End Date</label>
                                    <input type="date" name="end_date" class="form-control" value="{{ end_date|date:'Y-m-d' }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary btn-block">
                                        <i class="fas fa-filter mr-2"></i> Apply Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensils text-primary"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ total_meals }}</h3>
                            <h6>Total Meals</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ meals_by_status.delivered|default:0 }}</h3>
                            <h6>Delivered Meals</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensil-spoon text-secondary"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ meals_by_status.consumed|default:0 }}</h3>
                            <h6>Consumed Meals</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-undo-alt text-danger"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ meals_by_status.returned|default:0 }}</h3>
                            <h6>Returned Meals</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meals by Type</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="mealTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meals by Status</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="mealStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Daily Meal Count -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Daily Meal Count</h5>
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="dailyMealChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Export Options -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Export Reports</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 col-sm-6 mb-3">
                            <button class="btn btn-primary btn-block">
                                <i class="fas fa-file-excel mr-2"></i> Export to Excel
                            </button>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <button class="btn btn-info btn-block">
                                <i class="fas fa-file-pdf mr-2"></i> Export to PDF
                            </button>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <button class="btn btn-secondary btn-block">
                                <i class="fas fa-print mr-2"></i> Print Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .chart-container {
        position: relative;
        height: 300px;
    }
</style>

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Meal Type Chart
        var mealTypeCtx = document.getElementById('mealTypeChart').getContext('2d');
        var mealTypeChart = new Chart(mealTypeCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for meal_type in meals_by_type %}
                    '{{ meal_type_choices|get_item:meal_type.meal__meal_type }}',
                    {% empty %}
                    'Breakfast', 'Lunch', 'Dinner', 'Snack'
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for meal_type in meals_by_type %}
                        {{ meal_type.count }},
                        {% empty %}
                        25, 30, 28, 17
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#FF6384',
                        '#36A2EB',
                        '#FFCE56',
                        '#4BC0C0'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Meal Status Chart
        var mealStatusCtx = document.getElementById('mealStatusChart').getContext('2d');
        var mealStatusChart = new Chart(mealStatusCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    {% for status in meals_by_status %}
                    '{{ status_choices|get_item:status.status }}',
                    {% empty %}
                    'Preparing', 'Ready', 'In Transit', 'Delivered', 'Consumed', 'Returned'
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for status in meals_by_status %}
                        {{ status.count }},
                        {% empty %}
                        15, 10, 5, 30, 25, 5
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#FFC107',
                        '#17A2B8',
                        '#007BFF',
                        '#28A745',
                        '#6C757D',
                        '#DC3545'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Daily Meal Chart
        var dailyMealCtx = document.getElementById('dailyMealChart').getContext('2d');
        var dailyMealChart = new Chart(dailyMealCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for day in daily_counts %}
                    '{{ day.date }}',
                    {% empty %}
                    'May 1', 'May 2', 'May 3', 'May 4', 'May 5', 'May 6', 'May 7'
                    {% endfor %}
                ],
                datasets: [{
                    label: 'Number of Meals',
                    data: [
                        {% for day in daily_counts %}
                        {{ day.count }},
                        {% empty %}
                        12, 19, 15, 17, 14, 13, 16
                        {% endfor %}
                    ],
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    });
</script>
{% endblock %}
{% endblock %}
