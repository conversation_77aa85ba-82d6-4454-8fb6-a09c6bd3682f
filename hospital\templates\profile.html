{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">
    {% if user.user_type == '1' %}
    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Profile Details</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Profile Details</li>
    </ul>
    </div>
    </div>
    </div>
     {% elif user.user_type == '2' %}
    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Profile Details</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Profile Details</li>
    </ul>
    </div>
    </div>
    </div>
    {% else  %}
    <div class="page-header">
        <div class="row">
        <div class="col">
        <h3 class="page-title">Profile Details</h3>
        <ul class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'userhome' %}">Dashboard</a></li>
        <li class="breadcrumb-item active">Profile Details</li>
        </ul>
        </div>
        </div>
        </div>{% endif %}
    
    <div class="row">
    <div class="col-lg-12">
    <div class="card">
    <div class="card-header">
    <h5 class="card-title">Profile Details</h5>
    </div>
    <div class="card-body">
        {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'error' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                           {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'success' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                       <form method="POST" action="{% url 'profile_update' %}" enctype="multipart/form-data">
                        {% csrf_token %}
    <div class="form-group row">
    <label class="col-form-label col-md-2">Profile Pic</label>
    <div class="col-md-10">
        <input type="file" class="form-control" name="profile_pic">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">First Name</label>
    <div class="col-md-10">
        <input type="text" class="form-control" name="first_name" value="{{user.first_name}}">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Last Name</label>
    <div class="col-md-10">
        <input type="text" class="form-control" name="last_name" value="{{user.last_name}}">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Email</label>
    <div class="col-md-10">
        <input type="email" class="form-control" readonly="True" name="email" value="{{user.email}}">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Username</label>
    <div class="col-md-10">
        <input type="text" class="form-control" readonly="True" name="username" value="{{user.username}}">
    </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-10">
            <button type="submit" class="btn btn-dark">Update</button>
        </div>
    </div>
    
    </form>
    </div>
    </div>

    </div>
    </div>
    </div>



{% endblock %}