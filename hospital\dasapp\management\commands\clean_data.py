from django.core.management.base import BaseCommand
from django.db import connection
from dasapp.models import (
    <PERSON><PERSON><PERSON>, DoctorReg, PatientReg, Appointment, 
    Specialization, AddPatient, MedicalHistory, Page
)

class Command(BaseCommand):
    help = 'Cleans up all data except admin users'

    def handle(self, *args, **options):
        # Get admin user IDs to preserve
        admin_ids = list(CustomUser.objects.filter(user_type='1').values_list('id', flat=True))
        
        if not admin_ids:
            self.stdout.write(self.style.WARNING('No admin users found. Aborting cleanup.'))
            return
        
        self.stdout.write(f"Preserving admin users with IDs: {admin_ids}")
        
        # Delete all appointments
        appointment_count = Appointment.objects.all().count()
        Appointment.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {appointment_count} appointments'))
        
        # Delete all medical histories
        med_history_count = MedicalHistory.objects.all().count()
        MedicalHistory.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {med_history_count} medical histories'))
        
        # Delete all patients (AddPatient)
        add_patient_count = AddPatient.objects.all().count()
        AddPatient.objects.all().delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {add_patient_count} patients (AddPatient)'))
        
        # Delete all patient registrations except those linked to admin users
        patient_count = PatientReg.objects.exclude(admin_id__in=admin_ids).count()
        PatientReg.objects.exclude(admin_id__in=admin_ids).delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {patient_count} patient registrations'))
        
        # Delete all doctor registrations except those linked to admin users
        doctor_count = DoctorReg.objects.exclude(admin_id__in=admin_ids).count()
        DoctorReg.objects.exclude(admin_id__in=admin_ids).delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {doctor_count} doctor registrations'))
        
        # Delete all users except admins
        user_count = CustomUser.objects.exclude(id__in=admin_ids).count()
        CustomUser.objects.exclude(id__in=admin_ids).delete()
        self.stdout.write(self.style.SUCCESS(f'Deleted {user_count} non-admin users'))
        
        # Keep specializations and pages as they are configuration data
        self.stdout.write(self.style.SUCCESS('Data cleanup completed successfully'))
