{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">

    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Add Specialization</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Add Specialization</li>
    </ul>
    </div>
    </div>
    </div>
    
    <div class="row">
    <div class="col-lg-12">
    <div class="card">
    <div class="card-header">
    <h5 class="card-title">Add Specialization</h5>
    </div>
    <div class="card-body">
        {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'error' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                           {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'success' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                       <form method="POST" action="{% url 'add_specilizations' %}" enctype="multipart/form-data">
                        {% csrf_token %}
   
    <div class="form-group row">
    <label class="col-form-label col-md-4">Specialization Name</label>
    <div class="col-md-10">
        <input type="text" class="form-control" name="specializationname" value="">
    </div>
    </div>
    
    <div class="form-group row">
        <div class="col-sm-10">
            <button type="submit" class="btn btn-dark">Add</button>
        </div>
    </div>
    
    </form>
    </div>
    </div>

    </div>
    </div>
    </div>



{% endblock %}