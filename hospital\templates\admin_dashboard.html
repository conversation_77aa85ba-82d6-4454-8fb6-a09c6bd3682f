{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Welcome {{ user.first_name }}!</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item active">Dashboard</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Overview Statistics -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-user-md text-primary"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ doctor_count }}</h3>
                            <h6>Doctors</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-users text-success"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ patient_count }}</h3>
                            <h6>Patients</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-calendar-check text-warning"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ appointment_count }}</h3>
                            <h6>Appointments</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensils text-info"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ meal_count }}</h3>
                            <h6>Meals Today</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Overview Statistics -->

    <div class="row">
        <!-- Recent Appointments -->
        <div class="col-md-6 d-flex">
            <div class="card card-table flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Recent Appointments</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Patient Name</th>
                                    <th>Doctor</th>
                                    <th>Date</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in appointments %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="#">{{ appointment.pat_id.admin.first_name }} {{ appointment.pat_id.admin.last_name }}</a>
                                        </h2>
                                    </td>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="#">Dr. {{ appointment.doctor_id.admin.first_name }} {{ appointment.doctor_id.admin.last_name }}</a>
                                        </h2>
                                    </td>
                                    <td>{{ appointment.date_of_appointment }}</td>
                                    <td>
                                        {% if appointment.status == 'Pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                        {% elif appointment.status == 'Approved' %}
                                        <span class="badge badge-success">Approved</span>
                                        {% elif appointment.status == 'Completed' %}
                                        <span class="badge badge-info">Completed</span>
                                        {% elif appointment.status == 'Cancelled' %}
                                        <span class="badge badge-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No appointments found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Recent Appointments -->

        <!-- Today's Meals -->
        <div class="col-md-6 d-flex">
            <div class="card card-table flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Today's Meals</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for meal in meals %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="#">{{ meal.meal.name }}</a>
                                        </h2>
                                    </td>
                                    <td>{{ meal.patient.name }}</td>
                                    <td>{{ meal.scheduled_time|time:"h:i A" }}</td>
                                    <td>
                                        {% if meal.status == 'preparing' %}
                                        <span class="badge badge-warning">Preparing</span>
                                        {% elif meal.status == 'ready' %}
                                        <span class="badge badge-info">Ready</span>
                                        {% elif meal.status == 'in_transit' %}
                                        <span class="badge badge-primary">In Transit</span>
                                        {% elif meal.status == 'delivered' %}
                                        <span class="badge badge-success">Delivered</span>
                                        {% elif meal.status == 'consumed' %}
                                        <span class="badge badge-secondary">Consumed</span>
                                        {% elif meal.status == 'returned' %}
                                        <span class="badge badge-danger">Returned</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No meals scheduled for today</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Today's Meals -->
    </div>

    <div class="row">
        <!-- Doctor Distribution -->
        <div class="col-md-6 d-flex">
            <div class="card flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Doctor Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="doctor-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <!-- /Doctor Distribution -->

        <!-- Patient Activity -->
        <div class="col-md-6 d-flex">
            <div class="card flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Patient Activity</h5>
                </div>
                <div class="card-body">
                    <div id="patient-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <!-- /Patient Activity -->
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'add_doctor' %}">
                                        <i class="fas fa-user-md fa-3x mb-3 text-primary"></i>
                                        <h5>Add Doctor</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'add_patient' %}">
                                        <i class="fas fa-user-plus fa-3x mb-3 text-success"></i>
                                        <h5>Add Patient</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'create_meal_plan' %}">
                                        <i class="fas fa-clipboard-list fa-3x mb-3 text-warning"></i>
                                        <h5>Create Meal Plan</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'website_update' %}">
                                        <i class="fas fa-cog fa-3x mb-3 text-info"></i>
                                        <h5>Website Settings</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Quick Links -->
</div>
{% endblock %}

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Doctor Distribution Chart
        var doctorOptions = {
            series: [44, 55, 13, 33, 22],
            chart: {
                type: 'donut',
                height: 300
            },
            labels: ['Cardiology', 'Neurology', 'Orthopedics', 'Pediatrics', 'Other'],
            colors: ['#4361ee', '#3f37c9', '#4cc9f0', '#4895ef', '#f72585'],
            legend: {
                position: 'bottom'
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        var doctorChart = new ApexCharts(document.querySelector("#doctor-chart"), doctorOptions);
        doctorChart.render();

        // Patient Activity Chart
        var patientOptions = {
            series: [{
                name: 'Appointments',
                data: [31, 40, 28, 51, 42, 82, 56]
            }, {
                name: 'Admissions',
                data: [11, 32, 45, 32, 34, 52, 41]
            }],
            chart: {
                height: 300,
                type: 'area',
                toolbar: {
                    show: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth'
            },
            xaxis: {
                type: 'datetime',
                categories: [
                    "2023-05-01", "2023-05-02", "2023-05-03", "2023-05-04", 
                    "2023-05-05", "2023-05-06", "2023-05-07"
                ]
            },
            colors: ['#4361ee', '#f72585'],
            tooltip: {
                x: {
                    format: 'dd/MM/yy'
                },
            },
        };

        var patientChart = new ApexCharts(document.querySelector("#patient-chart"), patientOptions);
        patientChart.render();
    });
</script>
{% endblock %}
