from django.core.management.base import BaseCommand
from django.utils import timezone
from dasapp.models import Page

class Command(BaseCommand):
    help = 'Creates a basic Page record for the website'

    def handle(self, *args, **options):
        page, created = Page.objects.get_or_create(
            id=1,
            defaults={
                'pagetitle': 'Hospital Management System',
                'address': '123 Medical Center Drive, Healthcare City',
                'aboutus': 'Our Hospital Management System is dedicated to providing excellent healthcare services with a focus on patient care and efficient hospital operations. We strive to deliver the highest quality medical services using modern technology and compassionate care.',
                'email': '<EMAIL>',
                'mobilenumber': **********,
                'created_at': timezone.now(),
                'updated_at': timezone.now()
            }
        )
        
        if created:
            self.stdout.write(self.style.SUCCESS('Created basic Page record'))
        else:
            # Update the existing page
            page.pagetitle = 'Hospital Management System'
            page.address = '123 Medical Center Drive, Healthcare City'
            page.aboutus = 'Our Hospital Management System is dedicated to providing excellent healthcare services with a focus on patient care and efficient hospital operations. We strive to deliver the highest quality medical services using modern technology and compassionate care.'
            page.email = '<EMAIL>'
            page.mobilenumber = **********
            page.updated_at = timezone.now()
            page.save()
            self.stdout.write(self.style.SUCCESS('Updated existing Page record'))
