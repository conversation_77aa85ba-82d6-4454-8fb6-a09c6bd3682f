#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to wait for database
wait_for_db() {
    print_status "Waiting for database to be ready..."
    
    if [ "$DATABASE_ENGINE" = "postgresql" ]; then
        while ! python -c "import socket; s=socket.socket(); s.settimeout(1); s.connect(('$DB_HOST', $DB_PORT)); s.close()" 2>/dev/null; do
            print_warning "PostgreSQL is unavailable - sleeping"
            sleep 1
        done
        print_status "PostgreSQL is up - continuing"
    elif [ "$DATABASE_ENGINE" = "mysql" ]; then
        while ! python -c "import socket; s=socket.socket(); s.settimeout(1); s.connect(('$DB_HOST', $DB_PORT)); s.close()" 2>/dev/null; do
            print_warning "MySQL is unavailable - sleeping"
            sleep 1
        done
        print_status "MySQL is up - continuing"
    else
        print_status "Using SQLite - no database wait required"
    fi
}

# Function to run database migrations
run_migrations() {
    print_status "Running database migrations..."
    python manage.py migrate --noinput
    
    if [ $? -eq 0 ]; then
        print_status "Database migrations completed successfully"
    else
        print_error "Database migrations failed"
        exit 1
    fi
}

# Function to collect static files
collect_static() {
    print_status "Collecting static files..."
    python manage.py collectstatic --noinput
    
    if [ $? -eq 0 ]; then
        print_status "Static files collected successfully"
    else
        print_error "Static files collection failed"
        exit 1
    fi
}

# Function to create superuser if it doesn't exist
create_superuser() {
    if [ "$DJANGO_SUPERUSER_USERNAME" ] && [ "$DJANGO_SUPERUSER_EMAIL" ] && [ "$DJANGO_SUPERUSER_PASSWORD" ]; then
        print_status "Creating superuser if it doesn't exist..."
        python manage.py shell << EOF
from django.contrib.auth import get_user_model
User = get_user_model()
if not User.objects.filter(username='$DJANGO_SUPERUSER_USERNAME').exists():
    User.objects.create_superuser('$DJANGO_SUPERUSER_USERNAME', '$DJANGO_SUPERUSER_EMAIL', '$DJANGO_SUPERUSER_PASSWORD')
    print('Superuser created successfully')
else:
    print('Superuser already exists')
EOF
    else
        print_warning "Superuser environment variables not set, skipping superuser creation"
    fi
}

# Function to load initial data
load_initial_data() {
    if [ -f "hmsk.sql" ] && [ "$LOAD_INITIAL_DATA" = "true" ]; then
        print_status "Loading initial data from hmsk.sql..."
        if [ "$DATABASE_ENGINE" = "sqlite3" ] || [ "$DATABASE_ENGINE" = "sqlite" ]; then
            sqlite3 db.sqlite3 < hmsk.sql
            print_status "Initial data loaded successfully"
        else
            print_warning "Initial data loading only supported for SQLite currently"
        fi
    fi
}

# Function to check application health
health_check() {
    print_status "Performing application health check..."
    python manage.py check --deploy
    
    if [ $? -eq 0 ]; then
        print_status "Application health check passed"
    else
        print_warning "Application health check failed, but continuing..."
    fi
}

# Main execution
main() {
    print_status "Starting Hospital Management System initialization..."
    
    # Wait for database if not SQLite
    if [ "$DATABASE_ENGINE" != "sqlite3" ] && [ "$DATABASE_ENGINE" != "sqlite" ]; then
        wait_for_db
    fi
    
    # Run migrations
    run_migrations
    
    # Collect static files
    collect_static
    
    # Create superuser
    create_superuser
    
    # Load initial data
    load_initial_data
    
    # Health check
    health_check
    
    print_status "Initialization completed successfully!"
    print_status "Starting application with command: $@"
    
    # Execute the main command
    exec "$@"
}

# Handle script termination
cleanup() {
    print_status "Shutting down gracefully..."
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Check if we're being sourced or executed
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
