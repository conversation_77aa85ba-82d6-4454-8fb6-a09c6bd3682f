# Production Environment Configuration
# IMPORTANT: Update all passwords and secrets before deploying to production!

SECRET_KEY=CHANGE_THIS_TO_A_SECURE_RANDOM_STRING_IN_PRODUCTION
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com

# Database (PostgreSQL recommended for production)
DATABASE_ENGINE=postgresql
DB_NAME=hospital_db
DB_USER=postgres
DB_PASSWORD=CHANGE_THIS_SECURE_PASSWORD
DB_HOST=db
DB_PORT=5432

# Redis for caching and sessions
REDIS_URL=redis://:CHANGE_THIS_REDIS_PASSWORD@redis:6379/1
REDIS_PASSWORD=CHANGE_THIS_REDIS_PASSWORD

# Production superuser
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=CHANGE_THIS_ADMIN_PASSWORD

# Email configuration
EMAIL_HOST=smtp.your-email-provider.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=YOUR_EMAIL_PASSWORD

# CORS for production
CORS_ALLOWED_ORIGINS=https://your-domain.com,https://www.your-domain.com

# SSL and Security (uncomment for HTTPS)
# SECURE_SSL_REDIRECT=True
# SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# Monitoring
# SENTRY_DSN=https://<EMAIL>/project-id

# Backup settings
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
