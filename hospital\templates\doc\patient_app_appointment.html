{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">

    <div class="page-header">
    <div class="row align-items-center">
    <div class="col">
    <h3 class="page-title">Appointments</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">View Appointment</li>
    </ul>
    </div>
    
    </div>
    </div>
    
    <div class="row">
    <div class="col-sm-12">
    <div class="card card-table">
    <div class="card-body">
        {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'error' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
           {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'success' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
    <div class="table-responsive">
    <table class="table table-hover table-center mb-0 datatable">
    <thead>
    <tr>
   
    <th>Appointment Number</th>
    <th>Patient Name</th>
    <th>Date of Appointment</th>
    <th>Time of Appointment</th>
    <th>Creation Date</th>
    <th>Status</th>
    <th class="text-right">Action</th>
    </tr>
    </thead>
    <tbody>
        {% for i in patientdetails1 %}
    <tr>

    <td>{{i.appointmentnumber}}</td>
    <td>{{i.pat_id.admin.first_name}} {{i.pat_id.admin.last_name}}</td>
    <td>{{i.date_of_appointment}}</td>
    <td>{{i.time_of_appointment}}</td>
    <td>{{i.created_at}}</td>
    {% if i.status == '0' %}
                                                <td>Not Updated Yet</td>
                                                {% else %}
                                                <td>{{ i.status}}</td>{% endif %}
    <td class="text-right">
    <div class="actions">
    <a href="{% url 'patientappointmentdetails' i.id %}">
    <i class="btn btn-primary btn-block">View</i>
    </a>
  
    </div>
    </td>
    </tr> {% endfor %}
   
    </tbody>
    </table>
    
<!-- Pagination controls -->
<div class="pagination" style="padding-top: 20px;padding-bottom: 20px;text-align: center;">
    <span class="step-links">
        {% if view_appointment.has_previous %}
            <a href="?page=1">&laquo; First</a>
            <a href="?page={{ view_appointment.previous_page_number }}">Previous</a>
        {% endif %}

        <span class="current">
            Page {{ view_appointment.number }} of {{ view_appointment.paginator.num_pages }}.
        </span>

        {% if view_appointment.has_next %}
            <a href="?page={{ view_appointment.next_page_number }}">Next</a>
            <a href="?page={{ view_appointment.paginator.num_pages }}">Last &raquo;</a>
        {% endif %}
    </span>
</div>
    </div>
    </div>
    </div>
    </div>
    </div>
    </div>


{% endblock %}