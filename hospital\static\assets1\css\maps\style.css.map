{"version": 3, "sources": ["_media-query.scss", "_typography.scss", "_variables.scss", "_common.scss", "style.css", "templates/_navigation.scss", "templates/_backgrounds.scss", "templates/_slider.scss", "templates/_about.scss", "templates/_feature.scss", "templates/_counter.scss", "templates/_service.scss", "templates/_department.scss", "templates/_cta.scss", "templates/_review.scss", "templates/_contact.scss", "templates/_blog.scss", "templates/_single-post.scss", "templates/_blog-sidebar.scss", "_mixins.scss", "templates/_footer.scss"], "names": [], "mappings": "AAAA,uBAAuB;ACAvB;;;;;;;EAOE;AAGF,2FAAY;AACZ;EACE,mBAAkB;CACnB;;AAED;EACE,iBAAgB;EAChB,kCCPkC;EDQlC,oCAAmC;EACnC,gBAAe;EACf,eCjBiB;EDkBjB,iBAAgB;CAEjB;;AACD;EACE,+BChB8B;EDiB9B,iBAAe;EACf,YCtBS;CDuBV;;AAED;EACE,kBAAiB;CAElB;;AAED;EACE,gBAAe;CAChB;;AAED;EACE,kBAAiB;CAClB;;AAED;EACE,kBAAiB;EACjB,kBAAiB;CAClB;;AAED;EACE,mBAAkB;CACnB;;AAED;EACE,gBAAe;CAChB;;AAGD;EACE,kBAAiB;CAClB;;AE1DD;EACE,oBDDqB;CCEtB;;AAED;EACE,iBAAe;EACf,aAAW;EACX,cAAa;EACb,gBAAc;CAKf;;AATD;EAMI,iBAAgB;EAChB,0BDXmB;CCYpB;;AAIH;EACE,iBAAgB;EAChB,iBAAgB;CAKjB;;AAPD;EAII,iBAAe;EACf,0BDrBmB;CCsBpB;;AAKH;EACE,kBAAiB;CAClB;;AAID;EACE,sBAAqB;EACrB,gBAAe;EACf,qBAAoB;EACpB,iBAAgB;EAChB,qBAAmB;EACnB,qBAAoB;EACpB,+BDhC8B;ECiC9B,0BAAyB;EACzB,mBAAiB;EACjB,8BAA4B;EAMpB,0BAAyB;CAalC;;AA7BD;EAoBQ,iDAA2C;EAC3C,mBAAkB;CACnB;;AAtBP;EA0BI,aAAY;EACZ,iBAAgB;CACjB;;AAGH;EACE,oBDjEqB;ECkErB,YDnEU;ECoEV,sBDnEqB;CC0EtB;;AAVD;EAMI,oBDrEoB;ECsEpB,sBDtEoB;ECuEpB,YDzEQ;CC0ET;;AAEH;EACE,oBD3EsB;EC4EtB,YD9EU;EC+EV,sBD7EsB;CCoFvB;;AAVD;EAMI,oBDjFmB;ECkFnB,YDnFQ;ECoFR,sBDnFmB;CCoFpB;;AAIH;EACE,0BDzFqB;EC0FrB,wBAAsB;EACtB,eD3FqB;CC6GtB;;AArBD;EAMI,0BD9FmB;EC+FnB,YDhGQ;ECiGR,oBDhGmB;CCsGpB;;AAdH;EAWU,iDAA2C;CAC5C;;AAZT;EAiBQ,2CAAqC;CACtC;;AAMP;EACE,wBAAsB;EACtB,YD9GS;EC+GT,sBDjHiB;CCsHlB;;AARD;EAKI,oBDnHe;ECoHf,YDvHQ;CCwHT;;AAEH;EACE,iBD3HU;EC4HV,mBD5HU;EC6HV,YDxHS;CC+HV;;AAVD;EAMI,oBD/HmB;ECgInB,YDjIQ;ECkIR,sBDjImB;CCkIpB;;AAGH;EACE,mBDvIU;ECwIV,YDxIU;CC8IX;;AARD;EAKI,iBD3IQ;EC4IR,YDvIO;CCwIR;;AAIH;EACE,mBAAiB;CAClB;;AACD;EACE,oBAAkB;CACnB;;AAGD;EACE,WAAU;CACX;;AAKD;EACE,oBD3JW;CC4JZ;;AACD;EACE,oBDnKqB;CCoKtB;;AACD;EACE,oBAAsC;CACvC;;AACD;EACE,oBAAsC;CACvC;;AAED;EACE,iBDzKS;CC0KV;;AAGD;EACI,uGAAsG;EACtG,4BAA2B;CAC9B;;AAID;EACE,iBAAe;CAChB;;AACD;EACE,gBAAc;CACf;;AAED;EACE,sBAAqB;CACtB;;AAED;EACE,eDpMqB;ECqMrB,gBAAe;EACf,oBAAmB;CACpB;;AAKD;EAEI,YAAW;EACX,mBAAkB;EAClB,QAAO;EACP,OAAM;EACN,UAAS;EACT,SAAQ;EACR,YAAW;EACX,aAAY;EACZ,aAAY;EACZ,oBDvNmB;CCwNpB;;AAIH;EACE,mBAAkB;CAYnB;;AAbD;EAGI,YAAW;EACX,mBAAkB;EAClB,QAAO;EACP,OAAM;EACN,UAAS;EACT,SAAQ;EACR,YAAW;EACX,aAAY;EACZ,+BAA2B;CAC5B;;AAKH;EACE,gBAAe;CAChB;;AACD;EACE,mBAAkB;CACnB;;AACD;EACE,mBAAiB;CAClB;;AAED;EACE,oBACF;CAAC;;AAGD,WAAW;AACX;EACE,YD1PS;EC2PT,sBAAqB;EAKb,0BAAyB;CAClC;;AAED;EACE,eDvQsB;ECwQtB,sBAAqB;CACtB;;AAED;EACE,cAAa;CACd;;AAGD;EACE,gBAAe;EACf,kBAAiB;CAClB;;AAID;EACE,4BAA0B;EAC1B,mBAAkB;CAgBnB;;AAlBD;EAMM,YD/RM;CCgSP;;AAPL;EASM,YDlSM;CCmSP;;AAVL;EAcI,iBAAgB;EAChB,kBAAiB;EACjB,gDAA2C;CAC5C;;AAKH;EAEI,cAAa;CACd;;AH3SD;EGgTC;IACG,kBAAiB;IACjB,kBAAiB;GACpB;CCgCF;;AD7BD;EACE,eD1TkB;CC2TnB;;AAGD;EACE,oBD/TkB;CCgUnB;;AAGD;EACE,oBAAmB;CAIpB;;AALD;EAGI,eDtUgB;CCuUjB;;AAIH;EACE,gBAAc;CACf;;AACD;EACE,oBAAkB;CACnB;;AH7UC;EGgVA;IACE,gBAAe;GAChB;CC+BF;;AJtXC;EG0VA;IACE,gBAAe;GAChB;CCgCF;;AC5XD;EACC,kBAAiB;CAiBjB;;AAlBD;EAII,iBAAgB;EAChB,mBAAkB;EAClB,YHHO;EGIP,+BHA4B;EGC5B,2BAA0B;EAC1B,gBAAe;EAKP,0BAAyB;CAElC;;AAIH;EACI,cAAa;CAChB;;AAED;EACE,iBAAgB;CACjB;;AAGD;EAGI,mBAAkB;EAClB,eAAc;EACd,iBHpCQ;EGqCR,iBAAgB;EAChB,UAAS;EACT,QAAO;EACP,WAAS;EACT,WAAU;EACV,aAAY;EACZ,mBAAkB;EAKlB,iCAAgC;EAChC,YAAU;EACV,8BAA6B;EAC7B,mBAAkB;CACnB;;AArBH;EAwBM,WAAU;EACV,oBAAmB;EACnB,UAAQ;CACT;;AA3BL;EA8BM,mBAAkB;EAClB,8BAA4B;EAC5B,wBAAuB;EACvB,iBAAgB;EAChB,YAAW;CAIZ;;AAtCL;EAoCQ,eHjEgB;CGkEjB;;AAaP;EACE,oBHjFqB;EGkFrB,gBAAe;EACf,kBAAgB;EAChB,0CAAqC;EACrC,YHtFU;CGuFX;;AAED;EAGM,YH5FM;EG6FN,mBAAiB;CAClB;;AAIL;EAGQ,YHrGI;EGsGJ,iBAAgB;EAChB,oBAAmB;CACpB;;AANP;EAeQ,YHjHI;EGkHJ,mBAAkB;CACnB;;ACnHP;EACC,yDAAwD;EACxD,uBAAsB;EACpB,mBAAkB;CACpB;;ACJD;EACE,mBAAkB;EAClB,iBAAgB;EAChB,iBLHU;EKIV,0DAAyD;EACzD,uBAAsB;EACtB,kBAAiB;CAclB;;AApBD;EASI,wBAAuB;CAUxB;;AAnBH;EAYM,gBAAe;EACf,iBAAgB;EAChB,uBAAqB;EACrB,2BAA0B;EAC1B,eLZc;CKaf;;AAKL;EACE,oBAAkB;CACnB;;AAGD;EACE,eL3BqB;CK4BtB;;AAED;EACE,eL9BsB;CK+BvB;;AAGD;EACE,YAAW;EACX,YAAU;EACV,oBLrCsB;CKsCvB;;APjCC;EO8CC;IACG,gBAAe;IACf,kBAAiB;GACpB;EAED;IACE,kBAAiB;IACjB,4BAA4B;GAC7B;CHkdF;;AJ7gBC;EOgEE;IACE,gBAAe;IACf,kBAAiB;GACpB;EAED;IACG,kBAAiB;IAClB,4BAA4B;GAC7B;CHgdF;;AJ9gBC;EOoEG;IACG,gBAAe;IACf,kBAAiB;GACpB;EAED;IACE,4BAA4B;GAC7B;CH6cJ;;AJnhBC;EO2EE;IACE,4BAA4B;GAC7B;CH4cJ;;AIziBD;EAEE,mBAAiB;EACjB,mDAAkD;CAClD;;AAOF;EACC,cAAa;EACb,oBAAmB;EACnB,oBAAmB;EACnB,cAAa;EACb,wBAAuB;EACvB,oBNZY;CMaZ;;AAMD;EACC,mBAAkB;CAqBlB;;AAtBD;EAGE,WAAU;CACV;;AAJF;EAME,mBAAkB;EAClB,YAAW;EACX,YAAW;EACX,aAAY;EACZ,oBNlCqB;EMmCrB,cAAa;CAUb;;AArBF;EAcG,YNvCS;CMwCT;;AAfH;EAkBG,mBAAkB;EAClB,gCAA0B;CAC1B;;AAKH;EACC,iBAAgB;CAUhB;;AAXD;EAGE,oBAAmB;EACnB,aAAY;EACZ,kCAA6B;CAC7B;;AANF;EASE,aAAW;CACX;;AAGF;EACC,mBAAkB;CAClB;;AC7DD;EACC,kBAAiB;CACjB;;AACD;EACC,mBAAkB;EAClB,iBAAgB;EAChB,mBAAkB;EAClB,uBPXW;EOYX,mCAAkC;EAClC,mDAA+C;CAiB/C;;AAvBD;EASG,gBAAe;EACf,ePhBoB;COiBpB;;AAXH;EAeE,ePrBqB;COsBrB;;AAhBF;EAmBE,gBAAe;CACf;;AAKF;EACC,qDAA+C;CAC/C;;AAED;EAEE,iBAAgB;EAChB,6CAAwC;CACxC;;ACxCF;EACC,mBAAkB;EAClB,2BAA0B;EAC1B,mBAAkB;CA0BlB;;AA7BD;EAKE,eAAc;EACd,iCAA4B;EAC5B,gBAAe;EACf,mBAAkB;EAClB,UAAS;EACT,WAAS;EACT,SAAO;EACP,oCAAmC;EAI3B,4BAA2B;CACnC;;AAjBF;EAoBE,gBAAe;EACf,YRrBU;CQsBV;;AAtBF;EA0BE,mBAAkB;EAClB,gCAA2B;CAC3B;;AAGF;EACC,qBAAoB;CACpB;;ACjCD;EACC,mBAAiB;CAsBjB;;AAvBD;EAIE,iBTJU;ESKV,cAAa;EACb,mBAAiB;CACjB;;AAPF;EAUE,YAAW;EACX,oBAAmB;CACnB;;AAZF;EAcE,eTZsB;CSatB;;AAfF;EAiBE,mBAAkB;CAClB;;AAlBF;EAqBE,YAAW;CACX;;AAMF;EACC,cAAa;EACb,iBAAgB;EAChB,sCAAiC;EACjC,4CAAuC;CAQvC;;AAZD;EAOE,YAAW;EACX,kBAAiB;EACjB,uBTrCU;CSsCV;;ACtCF;EACC,oBAAmB;CAQnB;;AATD;EAGE,oBAAmB;CAKnB;;AARF;EAMG,eVJqB;CUKrB;;AAQH;EAGG,mBAAkB;EAClB,gBAAe;EACf,2BAA0B;EAC1B,gBAAe;EACf,sBAAqB;EACrB,gBAAe;CA2Bf;;AAnCH;EAWI,4BAA0B;EAC1B,0BAAwB;EACxB,oBV1BoB;EU2BpB,YV7BQ;CU8BR;;AAfJ;EAkBI,4BAA0B;EAC1B,0BAAwB;CACxB;;AApBJ;EAsBI,4BAA0B;EAC1B,0BAAwB;EACxB,oBVrCoB;EUsCpB,YVxCQ;CUyCR;;AA1BJ;EA6BI,4BAA0B;EAC1B,0BAAwB;EACxB,oBV5CoB;EU6CpB,YV/CQ;CUgDR;;AAjCJ;EAuCK,mBAAkB;CACrB;;AAKF;EACC,iBAAgB;CAsBhB;;AAvBD;EAGE,iBAAgB;EAChB,mBAAkB;EAClB,sDAAqD;CAiBrD;;AAtBF;EAYU,0BAAyB;CAShC;;AArBH;EAeI,8BAA6B;EAIrB,sBAAqB;CAC7B;;AAKJ;EACC,kBAAiB;CACjB;;AAOD;EAEE,oBAAmB;EACnB,YV5FS;CUiGT;;AARF;EAKG,mBAAkB;EAClB,eVlGqB;CUmGrB;;AAIH;EACC,eVzGsB;CU0GtB;;AZpGC;EYwGD;IAEE,eAAc;GAId;EANF;IAIG,gBAAe;GACf;CRssBH;;AJxzBC;EYyHD;IAEE,eAAc;GAId;EANF;IAIG,gBAAe;GACf;CRmsBH;;AJvzBC;EY2HD;IAEE,eAAc;GAId;EANF;IAIG,gBAAe;GACf;CRgsBH;;AS50BD;EACC,2DAAyD;EACzD,uBAAsB;EACtB,mBAAkB;CAWlB;;AAdD;EAME,mBAAkB;EAClB,YAAW;EACX,UAAS;EACT,SAAO;EACP,YAAU;EACV,aAAW;EACX,oCAAiC;CACjC;;AAGF;EACC,oBAAmB;CACnB;;AAED;EACC,eXpBsB;CWqBtB;;AAED;EACC,qBAAoB;CACpB;;AAID;EACC,qDAAoD;EACpD,mCAAkC;CAClC;;AAGD;EACC,qDAAmD;EACnD,uBAAsB;EACtB,mBAAkB;CAClB;;ACxCD;EACC,mBAAkB;CAclB;;AAfD;EAGE,WAAU;EACV,aAAW;EACX,OAAM;EACN,UAAQ;EACR,mBAAiB;EACjB,YAAU;EACV,2DAA0D;CAC1D;;AAVF;EAaE,iBAAgB;CAChB;;AAEF;EACC,mBAAkB;EAClB,oBAAmB;CA2BnB;;AA7BD;EAKE,iBZrBU;EYsBV,gBAAe;CAEf;;AARF;EAWE,oBAAmB;CAKnB;;AAhBF;EAcG,mBAAkB;CAClB;;AAfH;EAmBE,gBAAe;EACf,mBAAkB;EAClB,YAAW;EACX,aAAY;EACZ,aAAY;CACZ;;AAxBF;EA2BE,iBAAgB;CAChB;;AAOF;EAEE,mBAAkB;CAClB;;AAEF;EACC,iBZzDW;EY0DX,cAAa;EACb,gBAAe;EACf,oBAAmB;CA2BnB;;AA/BD;EAOE,YAAW;CAWX;;AAlBF;EAUG,YAAW;EACX,aAAW;EACX,oBAAkB;EAClB,mBAAkB;EAClB,oBAAmB;EACnB,0BZjEU;EYkEV,iBAAgB;CAChB;;AAjBH;EAqBE,YAAW;EACX,wBAAuB;CACvB;;AAvBF;EA0BK,cAAa;EACb,eZjFmB;EYkFnB,YAAW;CACd;;Ad9EA;EcyFD;IACC,iBAAgB;GAChB;EAED;IACC,cAAa;GACb;CV02BD;;AJ98BC;EcuGD;IACC,iBAAgB;GAChB;EACD;IACC,cAAa;GACb;CV22BD;;AJ78BC;EcqGD;IACC,iBAAgB;GAChB;EACD;IACC,cAAa;GACb;CV42BD;;AJj9BC;EcwGD;IACC,iBAAgB;GAChB;EACD;IACC,cAAa;GACb;CV62BD;;AW3+BD;EAEO,oBAAmB;CAQnB;;AAVP;EAIU,aAAW;EACX,0BAAyB;EACzB,iBAAgB;EAChB,YAAW;EACX,oBAAmB;CACpB;;AATT;EAYQ,oBAAmB;CAQpB;;AApBP;EAcU,aAAW;EACX,0BAAyB;EACzB,iBAAgB;EAChB,oBAAmB;EACnB,YAAW;CACZ;;AAKT;EAEI,cAAY;CACb;;AAHH;EAMI,mBAAkB;EAClB,gBAAe;CAChB;;AAGH;EACI,mBAAkB;CACrB;;AAED;EACI,YAAW;EACX,cAAa;CAChB;;AAID;EACE,iBAAgB;CACjB;;AAED;EACE,mBAAkB;EAClB,0BAAwB;EACxB,mBAAiB;CAOlB;;AAVD;EAKI,gBAAe;EACf,oBAAmB;EACnB,sBAAqB;EACrB,ebxDoB;CayDrB;;AC3DH;EAEE,iBAAgB;EAChB,gBAAe;CACf;;ACJF;;oEAEoE;AAEpE;EAEI,sBAAqB;EACrB,YAAW;EACX,aAAY;EACZ,oBAAmB;EACnB,iBAAgB;EAChB,mBAAkB;EAClB,kBAAiB;EACjB,iBAAgB;EAChB,mBAAkB;CAUnB;;AApBH;EAaM,oBfhBiB;EeiBjB,YflBM;CemBP;;AAfL;EAiBM,oBfpBiB;EeqBjB,YftBM;CeuBP;;AAQL;EAGI,mBAAkB;EAClB,oBAAmB;CACpB;;AALH;EAQI,gBAAe;EACf,iBAAgB;CACjB;;AAVH;EAYI,gBAAe;CAChB;;AAIH;EAEI,iBAAgB;CACjB;;AAMH;EACE,gBAAe;EACf,efvDkB;EewDlB,cAAa;EACb,mBAAkB;EAClB,+Bf5DsB;Ee6DtB,iBAAgB;CACjB;;AAED;EAEI,0Bf9DS;Ee+DT,kBAAiB;EACjB,efnEe;EeoEf,gBAAe;CAChB;;AAUH;EAEI,oBAAmB;EACnB,mBAAiB;EACjB,sBAAoB;EACpB,aAAY;CACb;;AANH;EASI,aAAW;CACZ;;AAcH;EACE,aAAW;CAIZ;;AALD;EAGI,iBAAe;CAChB;;AAEH;EACE,iCAA+B;EAC/B,qBAAmB;EACnB,oBAAmB;EACnB,0BAAyB;EACzB,gBAAe;EACf,oBAAkB;CACnB;;AACD;EACE,oBAAkB;CACnB;;AAED;EACE,eAAa;CAoCd;;AArCD;EAGI,iBAAe;CAIhB;;AAPH;EAKM,oBAAmB;CACpB;;AANL;EASI,cAAa;EACb,mBAAiB;EACjB,iBAAe;CAMhB;;AAjBH;EAaM,efxIiB;EeyIjB,gBAAe;EACf,0BAAyB;CAC1B;;AAhBL;EAmBI,gBAAc;EACd,sBAAqB;EACrB,eAAc;EACd,gBAAc;CACf;;AAvBH;EAyBI,efpJmB;EeqJnB,sBAAqB;EACrB,iBAAe;EACf,gBAAc;CAQf;;AApCH;EA8BM,kBAAgB;EAChB,sBAAqB;CACtB;;AAhCL;EAkCM,ef7JiB;Ce8JlB;;AAIL;EACE,oBAAmB;CAiBpB;;AAlBD;EAIM,YAAW;CACZ;;AALL;EAQI,iBAAgB;CACjB;;AATH;EAWI,eAAc;CAMf;;AAjBH;EAaM,kBAAiB;EACjB,gBAAe;EACf,efjLiB;CekLlB;;AAML;EACE,iBAAgB;CACjB;;AAGD;EACE,iBAAgB;CACjB;;AACD;EACE,mBAAkB;CACnB;;AAED;EACE,0BAAyB;EACzB,iBAAgB;EAChB,aAAY;EACZ,YAAW;CACZ;;AAID;EAEI,iBAAgB;CAIjB;;AANH;EAIM,oBAAmB;CACpB;;AAKL;EACE,0BAAyB;EACzB,mBAAkB;EAClB,kBAAiB;EACjB,cAAa;EACb,mBAAkB;CAsBnB;;AA3BD;EAOI,mBAAkB;CACnB;;AARH;EAWM,gBAAe;EACf,mBAAkB;CAKnB;;AAjBL;EAcQ,efrOe;EesOf,gBAAe;CAChB;;AAhBP;EAmBM,gBAAe;EACf,YAAW;CACZ;;AArBL;EAuBM,iBAAgB;CACjB;;AAML;EACE,iBAAgB;CAajB;;AAdD;EAGI,aAAY;EACZ,iBAAgB;EAChB,iBAAgB;CAKjB;;AAVH;EAOM,iBAAe;EACf,0Bf7PiB;Ce8PlB;;AATL;EAYI,aAAY;CACb;;ACjQH;EACE,oBAAkB;EAClB,qBAAmB;CA4FpB;;AA9FD;EAKI,oBAAkB;EAClB,mBAAkB;EAClB,qBAAoB;CAWrB;;AAlBH;EASM,mBAAkB;EAClB,YAAU;EACV,UAAS;EACT,YAAU;EACV,YAAW;EACX,YAAW;EACX,oBhBfkB;CgBgBnB;;AAhBL;EAwBQ,mBAAiB;CAClB;;AAzBP;EA2BQ,iBAAgB;EAChB,iBAAgB;CACjB;;AA7BP;EA+BQ,gBAAe;CAChB;;AAhCP;EAwCQ,oBAAmB;CAYpB;;AApDP;EA0CU,YhBvCC;EiBCP,0BDuC0C;CAKrC;;AAhDT;EA6CY,ehB9CW;EgB+CX,kBAAiB;CAClB;;AA/CX;EAkDU,kBAAiB;CAClB;;AAnDT;EA2DI,gBAAe;EACf,0BAAyB;EACzB,uBAAsB;EACtB,kBAAiB;EACjB,aAAY;EACZ,iBAAgB;EAChB,oBAAmB;EACnB,YAAW;EACX,sBAAqB;EACrB,0BAAyB;EACzB,qBAAoB;EACpB,gBAAe;EAGf,yBAAwB;CAMzB;;AA/EH;EA4EM,YhB9EM;EgB+EN,oBhB9EiB;CgB+ElB;;AA9EL;EAmFE,oBAAmB;EACnB,cAAa;CAOd;;AA3FD;EAuFM,kBAAiB;EACjB,8BAA4B;CAC7B;;AAYL;EACE,mBAAkB;CAMnB;;AAPD;EAGI,mBAAkB;EAClB,YAAW;EACX,SAAO;CACR;;AE7GH;EACC,qBAAoB;CAOpB;;AARD;EAKM,iBAAgB;CACjB;;AAKL;EACE,kBAAiB;CAClB;;AAGD;EAKE,iBAAgB;EAChB,oBAAmB;CAKpB;;AAXD;EAEI,elBjBmB;CkBkBpB;;AAHH;EASI,elBxBmB;CkByBpB;;AAIH;EAGI,YAAU;CACX;;AAJH;EAOM,elBpCiB;CkBqCpB;;AARH;EAWI,elBtCe;CkB2ChB;;AAhBH;EAcM,elB1CkB;CkB2CnB;;AAKL;EAEI,iBAAgB;EAChB,elBlDe;CkBmDhB;;AAJH;EAMI,gBAAe;CAChB;;AAIH;EACE,0CAAsC;CACvC;;AAED;EAEI,YAAW;EACX,aAAW;EACX,oBlBlEe;EkBmEf,YlBtEQ;EkBuER,sBAAqB;EACrB,mBAAkB;EAClB,oBAAkB;EAClB,kBAAiB;CAClB;;AAIH;EAEI,iBAAgB;EAChB,oBAAmB;CAKpB;;AARH;EAMM,elBnFkB;CkBoFnB;;AAML;EACE,mBAAkB;CAYnB;;AAbD;EAGI,oBAAkB;EAClB,aAAW;EACX,mBAAkB;EAClB,mBAAiB;CAClB;;AAPH;EASI,mBAAkB;EAClB,WAAS;EACT,SAAO;CACR;;AAMH;EACE,gBAAe;EACf,oBlB9GsB;EkB+GtB,cAAa;EACb,sBAAqB;EACrB,YAAW;EACX,YAAW;EACX,aAAW;EACX,aAAY;EACZ,mBAAkB;EAClB,cAAa;EACb,wBAAuB;EACvB,oBAAmB;EACnB,WAAU;EACV,oBAAmB;CAMpB;;AApBD;EAiBI,YlB/HQ;EkBgIR,gBAAe;CAChB;;AAKH;EACI,oBAAmB;EACnB,gBAAe;EACf,WAAU;CACb", "file": "../style.css", "sourcesContent": ["/*=== MEDIA QUERY ===*/\n@mixin mobile-xs{\n  @media(max-width:400px){\n    @content;\n  }\n}\n@mixin mobile{\n  @media(max-width:480px){\n    @content;\n  }\n}\n@mixin tablet{\n  @media(max-width:768px){\n    @content;\n  }\n}\n@mixin desktop{\n  @media(max-width:992px){\n    @content;\n  }\n}\n@mixin large-desktop{\n  @media(max-width:1200px){\n    @content;\n  }\n}", "/*\nTheme Name: Medic\nAuthor: Themefisher\nAuthor URI: https://themefisher.com/\nDescription: Medicle Template\nVersion: 1.0.0\n\n*/\n\n// Fonts \n@import url('https://fonts.googleapis.com/css?family=Exo:500,600,700|Roboto&display=swap');\nhtml{\n  overflow-x: hidden;\n}\n\nbody {\n  line-height: 1.6;\n  font-family: $secondary-font;\n  -webkit-font-smoothing: antialiased;\n  font-size: 16px;\n  color: $base-color;\n  font-weight: 400;\n\n}\nh1,.h1,h2,.h2,h3,.h3,h4,.h4,h5,.h5,h6,.h6 {\n  font-family:$primary-font ;\n  font-weight:700;\n  color:$black;\n}\n\nh1 ,.h1{\n  font-size: 2.5rem;\n  \n}\n\nh2,.h2 {\n  font-size: 44px;\n}\n\nh3,.h3 {\n  font-size: 1.5rem;\n}\n\nh4,.h4 {\n  font-size: 1.3rem;\n  line-height: 30px;\n}\n\nh5,.h5 {\n  font-size: 1.25rem;\n}\n\nh6,.h6 {\n  font-size: 1rem;\n}\n\n\np{\n  line-height: 30px;\n}", "$light: #fff;\n$primary-color: #223a66;\n$secondary-color:#e12454;\n$base-color:#6F8BA4;\n$title-color:#223a66;\n$black:#222;\n$gray:#eff0f3;\n$secondary-bg:#f4f9fc;\n$border-color:#fafafa;\n$primary-font: 'Exo', sans-serif;\n$secondary-font:'Roboto', sans-serif;\n\n$grad-color:linear-gradient(to bottom,#05a5f9,#10dab7);", "\n.navbar-toggle .icon-bar {\n  background: $primary-color;\n}\n\ninput[type=\"email\"],input[type=\"password\"],input[type=\"text\"],input[type=\"tel\"]{\n  box-shadow:none;\n  height:45px;\n  outline: none;\n  font-size:14px;\n  &:focus {\n    box-shadow: none;\n    border:1px solid $primary-color;\n  }\n}\n\n\n.form-control {\n  box-shadow: none;\n  border-radius: 0;\n  &:focus {\n    box-shadow:none;\n    border:1px solid $primary-color;\n  }\n}\n\n//\n\n.py-7{\n  padding: 7rem 0px;\n}\n\n// Button Style\n\n.btn{\n  display: inline-block;\n  font-size: 14px;\n  font-size: 0.8125rem;\n  font-weight: 700;\n  letter-spacing:.5px;\n  padding: .75rem 2rem;\n  font-family: $primary-font;\n  text-transform: uppercase;\n  border-radius:5px;\n  border:2px solid transparent;\n  // box-shadow: 0 11px 22px rgba(34, 34, 34, 0.2);\n  -webkit-transition: all .35s ease;\n     -moz-transition: all .35s ease;\n      -ms-transition: all .35s ease;\n       -o-transition: all .35s ease;\n          transition: all .35s ease;\n\n   &.btn-icon {\n    i {\n        border-left:1px solid rgba(255,255,255,.09);\n        padding-left: 15px;    \n      }\n  }\n\n  &:focus{\n    outline: 0px;\n    box-shadow: none;\n  }\n}\n\n.btn-main {\n  background: $primary-color;\n  color: $light;\n  border-color:$primary-color;\n \n  &:hover {\n    background:$secondary-color;\n    border-color:$secondary-color;\n    color: $light;\n  }\n}\n.btn-main-2 {\n  background: $secondary-color;\n  color: $light;\n  border-color:$secondary-color;\n \n  &:hover {\n    background: $primary-color;\n    color: $light;\n    border-color:$primary-color;\n  }\n}\n\n\n.btn-solid-border {\n  border:2px solid $primary-color;\n  background:transparent;\n  color:$primary-color;\n  \n  &:hover {\n    border:2px solid $primary-color;\n    color:$light;\n    background:$primary-color;\n    &.btn-icon {\n      i {\n          border-left:1px solid rgba(255,255,255,.09);\n        }\n    }\n  }\n  &.btn-icon {\n    i {\n        border-left:1px solid rgba(0,0,0,.09);\n      }\n  }\n\n}\n\n\n.btn-transparent {\n  background:transparent;\n  color:$black;\n  border-color:$base-color;\n  &:hover {\n    background:$base-color;\n    color:$light;\n  }\n}\n.btn-white{\n  background: $light;\n  border-color:$light;\n  color: $black;\n\n  &:hover{\n    background: $primary-color;\n    color: $light;\n    border-color:$primary-color;\n  }\n}\n\n.btn-solid-white{\n  border-color:$light;\n  color: $light;\n\n  &:hover{\n    background: $light;\n    color: $black;\n  }\n}\n\n\n.btn-round {\n  border-radius:4px;\n}\n.btn-round-full {\n  border-radius:50px;\n}\n\n\n.btn.active:focus, .btn:active:focus, .btn:focus {\n  outline: 0;\n}\n\n\n// Background\n\n.bg-gray {\n  background:$gray;\n}\n.bg-primary {\n  background:$primary-color;\n}\n.bg-primary-dark {\n  background:darken($primary-color, 10%);\n}\n.bg-primary-darker {\n  background:darken($primary-color, 20%);\n}\n\n.bg-dark {\n  background:$black;\n}\n\n\n.bg-gradient{\n    background-image: linear-gradient(145deg, rgba(19, 177, 205, 0.95) 0%, rgba(152, 119, 234, 0.95) 100%);\n    background-repeat: repeat-x;\n}\n\n\n//  Section Title\n.section {\n  padding:100px 0;\n}\n.section-sm {\n  padding:70px 0;\n}\n\n.section-bottom{\n  padding-bottom: 100px;\n}\n\n.subtitle {\n  color: $primary-color;\n  font-size: 14px;\n  letter-spacing: 1px;\n}\n\n\n\n\n.overlay {\n  &:before{\n    content: \"\";\n    position: absolute;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    right: 0;\n    width: 100%;\n    height: 100%;\n    opacity: 0.9;\n    background:$primary-color;\n  }\n}\n\n\n.overly-2 {\n  position: relative;\n  &:before{\n    content: \"\";\n    position: absolute;\n    left: 0;\n    top: 0;\n    bottom: 0;\n    right: 0;\n    width: 100%;\n    height: 100%;\n    background: rgba(0,0,0,0.8);\n  }\n}\n\n\n\n.text-sm{\n  font-size: 14px;\n}\n.text-md{\n  font-size: 2.25rem;\n}\n.text-lg{\n  font-size:3.75rem; \n}\n\n.no-spacing{\n  letter-spacing: 0px\n}\n\n\n/* Links */\na {\n  color: $black;\n  text-decoration: none;\n  -webkit-transition: all .35s ease;\n     -moz-transition: all .35s ease;\n      -ms-transition: all .35s ease;\n       -o-transition: all .35s ease;\n          transition: all .35s ease;\n}\n\na:focus, a:hover {\n  color: $secondary-color;\n  text-decoration: none;\n}\n\na:focus {\n  outline: none;\n}\n\n\n.content-title {\n  font-size: 40px;\n  line-height: 50px;\n}\n\n\n\n.page-title{\n  padding:120px 0px 70px 0px;\n  position: relative;\n  \n  .block{\n    h1{\n      color:$light;\n    }\n    p{\n      color:$light;\n    }\n  }\n\n  .breadcumb-nav{\n    margin-top: 60px;\n    padding-top: 30px;\n    border-top:1px solid rgba(255,255,255,0.06);\n  }\n}\n\n\n\n.slick-slide {\n  &:focus, a {\n    outline: none;\n  }\n}\n\n\n@include mobile{\n   h2, .h2 {\n      font-size: 1.3rem;\n      line-height: 36px;\n  }\n}\n\n.title-color{\n  color: $title-color;\n}\n\n\n.secondary-bg{\n  background: $title-color;;\n}\n\n\n.section-title {\n  margin-bottom: 70px;\n  h2{\n    color: $title-color;\n  }\n}\n\n\n.text-lg{\n  font-size:50px;\n}\n.gray-bg{\n  background:#f4f9fc;\n}\n\n@include mobile{\n  .text-lg{\n    font-size: 28px;\n  }\n}\n@include mobile-xs{\n  .text-lg{\n    font-size: 28px;\n  }\n}", "/*=== MEDIA QUERY ===*/\n/*\nTheme Name: Medic\nAuthor: Themefisher\nAuthor URI: https://themefisher.com/\nDescription: Medicle Template\nVersion: 1.0.0\n\n*/\n@import url(\"https://fonts.googleapis.com/css?family=Exo:500,600,700|Roboto&display=swap\");\nhtml {\n  overflow-x: hidden;\n}\n\nbody {\n  line-height: 1.6;\n  font-family: \"Roboto\", sans-serif;\n  -webkit-font-smoothing: antialiased;\n  font-size: 16px;\n  color: #6F8BA4;\n  font-weight: 400;\n}\n\nh1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6 {\n  font-family: \"Exo\", sans-serif;\n  font-weight: 700;\n  color: #222;\n}\n\nh1, .h1 {\n  font-size: 2.5rem;\n}\n\nh2, .h2 {\n  font-size: 44px;\n}\n\nh3, .h3 {\n  font-size: 1.5rem;\n}\n\nh4, .h4 {\n  font-size: 1.3rem;\n  line-height: 30px;\n}\n\nh5, .h5 {\n  font-size: 1.25rem;\n}\n\nh6, .h6 {\n  font-size: 1rem;\n}\n\np {\n  line-height: 30px;\n}\n\n.navbar-toggle .icon-bar {\n  background: #223a66;\n}\n\ninput[type=\"email\"], input[type=\"password\"], input[type=\"text\"], input[type=\"tel\"] {\n  box-shadow: none;\n  height: 45px;\n  outline: none;\n  font-size: 14px;\n}\n\ninput[type=\"email\"]:focus, input[type=\"password\"]:focus, input[type=\"text\"]:focus, input[type=\"tel\"]:focus {\n  box-shadow: none;\n  border: 1px solid #223a66;\n}\n\n.form-control {\n  box-shadow: none;\n  border-radius: 0;\n}\n\n.form-control:focus {\n  box-shadow: none;\n  border: 1px solid #223a66;\n}\n\n.py-7 {\n  padding: 7rem 0px;\n}\n\n.btn {\n  display: inline-block;\n  font-size: 14px;\n  font-size: 0.8125rem;\n  font-weight: 700;\n  letter-spacing: .5px;\n  padding: .75rem 2rem;\n  font-family: \"Exo\", sans-serif;\n  text-transform: uppercase;\n  border-radius: 5px;\n  border: 2px solid transparent;\n  -webkit-transition: all .35s ease;\n  -moz-transition: all .35s ease;\n  -ms-transition: all .35s ease;\n  -o-transition: all .35s ease;\n  transition: all .35s ease;\n}\n\n.btn.btn-icon i {\n  border-left: 1px solid rgba(255, 255, 255, 0.09);\n  padding-left: 15px;\n}\n\n.btn:focus {\n  outline: 0px;\n  box-shadow: none;\n}\n\n.btn-main {\n  background: #223a66;\n  color: #fff;\n  border-color: #223a66;\n}\n\n.btn-main:hover {\n  background: #e12454;\n  border-color: #e12454;\n  color: #fff;\n}\n\n.btn-main-2 {\n  background: #e12454;\n  color: #fff;\n  border-color: #e12454;\n}\n\n.btn-main-2:hover {\n  background: #223a66;\n  color: #fff;\n  border-color: #223a66;\n}\n\n.btn-solid-border {\n  border: 2px solid #223a66;\n  background: transparent;\n  color: #223a66;\n}\n\n.btn-solid-border:hover {\n  border: 2px solid #223a66;\n  color: #fff;\n  background: #223a66;\n}\n\n.btn-solid-border:hover.btn-icon i {\n  border-left: 1px solid rgba(255, 255, 255, 0.09);\n}\n\n.btn-solid-border.btn-icon i {\n  border-left: 1px solid rgba(0, 0, 0, 0.09);\n}\n\n.btn-transparent {\n  background: transparent;\n  color: #222;\n  border-color: #6F8BA4;\n}\n\n.btn-transparent:hover {\n  background: #6F8BA4;\n  color: #fff;\n}\n\n.btn-white {\n  background: #fff;\n  border-color: #fff;\n  color: #222;\n}\n\n.btn-white:hover {\n  background: #223a66;\n  color: #fff;\n  border-color: #223a66;\n}\n\n.btn-solid-white {\n  border-color: #fff;\n  color: #fff;\n}\n\n.btn-solid-white:hover {\n  background: #fff;\n  color: #222;\n}\n\n.btn-round {\n  border-radius: 4px;\n}\n\n.btn-round-full {\n  border-radius: 50px;\n}\n\n.btn.active:focus, .btn:active:focus, .btn:focus {\n  outline: 0;\n}\n\n.bg-gray {\n  background: #eff0f3;\n}\n\n.bg-primary {\n  background: #223a66;\n}\n\n.bg-primary-dark {\n  background: #152440;\n}\n\n.bg-primary-darker {\n  background: #090f1a;\n}\n\n.bg-dark {\n  background: #222;\n}\n\n.bg-gradient {\n  background-image: linear-gradient(145deg, rgba(19, 177, 205, 0.95) 0%, rgba(152, 119, 234, 0.95) 100%);\n  background-repeat: repeat-x;\n}\n\n.section {\n  padding: 100px 0;\n}\n\n.section-sm {\n  padding: 70px 0;\n}\n\n.section-bottom {\n  padding-bottom: 100px;\n}\n\n.subtitle {\n  color: #223a66;\n  font-size: 14px;\n  letter-spacing: 1px;\n}\n\n.overlay:before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 100%;\n  height: 100%;\n  opacity: 0.9;\n  background: #223a66;\n}\n\n.overly-2 {\n  position: relative;\n}\n\n.overly-2:before {\n  content: \"\";\n  position: absolute;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: 100%;\n  height: 100%;\n  background: rgba(0, 0, 0, 0.8);\n}\n\n.text-sm {\n  font-size: 14px;\n}\n\n.text-md {\n  font-size: 2.25rem;\n}\n\n.text-lg {\n  font-size: 3.75rem;\n}\n\n.no-spacing {\n  letter-spacing: 0px;\n}\n\n/* Links */\na {\n  color: #222;\n  text-decoration: none;\n  -webkit-transition: all .35s ease;\n  -moz-transition: all .35s ease;\n  -ms-transition: all .35s ease;\n  -o-transition: all .35s ease;\n  transition: all .35s ease;\n}\n\na:focus, a:hover {\n  color: #e12454;\n  text-decoration: none;\n}\n\na:focus {\n  outline: none;\n}\n\n.content-title {\n  font-size: 40px;\n  line-height: 50px;\n}\n\n.page-title {\n  padding: 120px 0px 70px 0px;\n  position: relative;\n}\n\n.page-title .block h1 {\n  color: #fff;\n}\n\n.page-title .block p {\n  color: #fff;\n}\n\n.page-title .breadcumb-nav {\n  margin-top: 60px;\n  padding-top: 30px;\n  border-top: 1px solid rgba(255, 255, 255, 0.06);\n}\n\n.slick-slide:focus, .slick-slide a {\n  outline: none;\n}\n\n@media (max-width: 480px) {\n  h2, .h2 {\n    font-size: 1.3rem;\n    line-height: 36px;\n  }\n}\n\n.title-color {\n  color: #223a66;\n}\n\n.secondary-bg {\n  background: #223a66;\n}\n\n.section-title {\n  margin-bottom: 70px;\n}\n\n.section-title h2 {\n  color: #223a66;\n}\n\n.text-lg {\n  font-size: 50px;\n}\n\n.gray-bg {\n  background: #f4f9fc;\n}\n\n@media (max-width: 480px) {\n  .text-lg {\n    font-size: 28px;\n  }\n}\n\n@media (max-width: 400px) {\n  .text-lg {\n    font-size: 28px;\n  }\n}\n\n#navbarmain {\n  padding: 20px 0px;\n}\n\n#navbarmain .nav-link {\n  font-weight: 600;\n  padding: 10px 15px;\n  color: #222;\n  font-family: \"Exo\", sans-serif;\n  text-transform: capitalize;\n  font-size: 16px;\n  -webkit-transition: all .25s ease;\n  -moz-transition: all .25s ease;\n  -ms-transition: all .25s ease;\n  -o-transition: all .25s ease;\n  transition: all .25s ease;\n}\n\n.dropdown-toggle::after {\n  display: none;\n}\n\n.navbar-brand {\n  margin-top: 10px;\n}\n\n.dropdown .dropdown-menu {\n  position: absolute;\n  display: block;\n  background: #fff;\n  min-width: 240px;\n  top: 130%;\n  left: 0;\n  right: 0px;\n  opacity: 0;\n  padding: 0px;\n  visibility: hidden;\n  -webkit-transition: all 0.3s ease-out 0s;\n  -moz-transition: all 0.3s ease-out 0s;\n  -ms-transition: all 0.3s ease-out 0s;\n  -o-transition: all 0.3s ease-out 0s;\n  transition: all 0.3s ease-out 0s;\n  border: 0px;\n  border-top: 5px solid #e12454;\n  border-radius: 0px;\n}\n\n.dropdown:hover .dropdown-menu {\n  opacity: 1;\n  visibility: visible;\n  top: 115%;\n}\n\n.dropdown .dropdown-item {\n  padding: 13px 20px;\n  border-bottom: 1px solid #eee;\n  background: transparent;\n  font-weight: 400;\n  color: #555;\n}\n\n.dropdown .dropdown-item:hover {\n  color: #e12454;\n}\n\n.header-top-bar {\n  background: #223a66;\n  font-size: 14px;\n  padding: 10px 0px;\n  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);\n  color: #fff;\n}\n\n.top-bar-info li a {\n  color: #fff;\n  margin-right: 20px;\n}\n\n.top-right-bar a span {\n  color: #fff;\n  font-weight: 600;\n  letter-spacing: 1px;\n}\n\n.top-right-bar a i {\n  color: #fff;\n  margin-right: 10px;\n}\n\n.bg-1 {\n  background: url(\"../images/bg/22.jpg\") no-repeat 50% 50%;\n  background-size: cover;\n  position: relative;\n}\n\n.banner {\n  position: relative;\n  overflow: hidden;\n  background: #fff;\n  background: url(\"../images/bg/slider-bg-1.jpg\") no-repeat;\n  background-size: cover;\n  min-height: 550px;\n}\n\n.banner .block {\n  padding: 80px 0px 160px;\n}\n\n.banner .block h1 {\n  font-size: 60px;\n  line-height: 1.2;\n  letter-spacing: -1.2px;\n  text-transform: capitalize;\n  color: #223a66;\n}\n\n.letter-spacing {\n  letter-spacing: 2px;\n}\n\n.text-color {\n  color: #223a66;\n}\n\n.text-color-2 {\n  color: #e12454;\n}\n\n.divider {\n  width: 40px;\n  height: 5px;\n  background: #e12454;\n}\n\n@media (max-width: 480px) {\n  .banner .block h1 {\n    font-size: 38px;\n    line-height: 50px;\n  }\n  .banner {\n    min-height: 450px;\n    background: #fff !important;\n  }\n}\n\n@media (max-width: 400px) {\n  .banner .block h1 {\n    font-size: 28px;\n    line-height: 40px;\n  }\n  .banner {\n    min-height: 450px;\n    background: #fff !important;\n  }\n}\n\n@media (max-width: 768px) {\n  .banner .block h1 {\n    font-size: 56px;\n    line-height: 70px;\n  }\n  .banner {\n    background: #fff !important;\n  }\n}\n\n@media (max-width: 992px) {\n  .banner {\n    background: #fff !important;\n  }\n}\n\n.about-img img {\n  border-radius: 5px;\n  box-shadow: 0px 0px 30px 0px rgba(0, 42, 106, 0.1);\n}\n\n.award-img {\n  height: 120px;\n  margin-bottom: 10px;\n  align-items: center;\n  display: flex;\n  justify-content: center;\n  background: #eff0f3;\n}\n\n.appoinment-content {\n  position: relative;\n}\n\n.appoinment-content img {\n  width: 85%;\n}\n\n.appoinment-content .emergency {\n  position: absolute;\n  content: \"\";\n  right: 10px;\n  bottom: 20px;\n  background: #223a66;\n  padding: 48px;\n}\n\n.appoinment-content .emergency h2 {\n  color: #fff;\n}\n\n.appoinment-content .emergency i {\n  margin-right: 10px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.appoinment-form {\n  margin-top: 40px;\n}\n\n.appoinment-form .form-control {\n  background: #f4f9fc;\n  height: 55px;\n  border-color: rgba(0, 0, 0, 0.05);\n}\n\n.appoinment-form textarea.form-control {\n  height: auto;\n}\n\n.client-thumb {\n  text-align: center;\n}\n\n.features {\n  margin-top: -70px;\n}\n\n.feature-item {\n  flex-basis: 33.33%;\n  margin: 0px 10px;\n  padding: 40px 30px;\n  background-color: #fff;\n  border-radius: 15px 15px 15px 15px;\n  box-shadow: 0px 0px 30px 0px rgba(0, 42, 106, 0.1);\n}\n\n.feature-item .feature-icon i {\n  font-size: 50px;\n  color: #223a66;\n}\n\n.feature-item h4 {\n  color: #223a66;\n}\n\n.feature-item p {\n  font-size: 14px;\n}\n\n.feature-section.border-top {\n  border-top: 1px solid rgba(0, 0, 0, 0.05) !important;\n}\n\n.w-hours li {\n  padding: 6px 0px;\n  border-bottom: 1px solid rgba(0, 0, 0, 0.05);\n}\n\n.counter-stat {\n  text-align: center;\n  padding: 55px 0px 40px 0px;\n  position: relative;\n}\n\n.counter-stat i {\n  display: block;\n  color: rgba(255, 255, 255, 0.06);\n  font-size: 70px;\n  position: absolute;\n  left: 0px;\n  right: 0px;\n  top: 0px;\n  -webkit-transform: translateY(25px);\n  -moz-transform: translateY(25px);\n  -ms-transform: translateY(25px);\n  -o-transform: translateY(25px);\n  transform: translateY(25px);\n}\n\n.counter-stat span {\n  font-size: 70px;\n  color: #fff;\n}\n\n.counter-stat p {\n  margin-bottom: 0px;\n  color: rgba(255, 255, 255, 0.7);\n}\n\n.mb--80 {\n  margin-bottom: -80px;\n}\n\n.service {\n  padding-top: 180px;\n}\n\n.service .service-item {\n  background: #fff;\n  padding: 30px;\n  border-radius: 5px;\n}\n\n.service .icon {\n  float: left;\n  margin-bottom: 10px;\n}\n\n.service i {\n  color: #e12454;\n}\n\n.service h4 {\n  padding-left: 20px;\n}\n\n.service .content {\n  clear: both;\n}\n\n.service-block {\n  padding: 20px;\n  margin-top: 40px;\n  border: 1px solid rgba(0, 0, 0, 0.03);\n  box-shadow: 0 0 38px rgba(21, 40, 82, 0.07);\n}\n\n.service-block img {\n  width: 100%;\n  margin-top: -60px;\n  border: 5px solid #fff;\n}\n\n.department-service {\n  margin-bottom: 40px;\n}\n\n.department-service li {\n  margin-bottom: 10px;\n}\n\n.department-service li i {\n  color: #e12454;\n}\n\n.doctors .btn-group .btn {\n  border-radius: 0px;\n  margin: 0px 2px;\n  text-transform: capitalize;\n  font-size: 16px;\n  padding: .6rem 1.5rem;\n  cursor: pointer;\n}\n\n.doctors .btn-group .btn.active {\n  box-shadow: none !important;\n  border-color: transparent;\n  background: #e12454;\n  color: #fff;\n}\n\n.doctors .btn-group .btn.focus {\n  box-shadow: none !important;\n  border-color: transparent;\n}\n\n.doctors .btn-group .btn:focus {\n  box-shadow: none !important;\n  border-color: transparent;\n  background: #e12454;\n  color: #fff;\n}\n\n.doctors .btn-group .btn:hover {\n  box-shadow: none !important;\n  border-color: transparent;\n  background: #e12454;\n  color: #fff;\n}\n\n.doctors .btn-group > .btn-group:not(:last-child) > .btn, .doctors .btn-group > .btn:not(:last-child):not(.dropdown-toggle), .doctors .btn-group > .btn:not(:first-child) {\n  border-radius: 3px;\n}\n\n.doctor-inner-box {\n  overflow: hidden;\n}\n\n.doctor-inner-box .doctor-profile {\n  overflow: hidden;\n  position: relative;\n  box-shadow: 0px 8px 16px 0px rgba(200, 183, 255, 0.2);\n}\n\n.doctor-inner-box .doctor-profile .doctor-img {\n  -webkit-transition: all .35s ease;\n  -moz-transition: all .35s ease;\n  -ms-transition: all .35s ease;\n  -o-transition: all .35s ease;\n  transition: all .35s ease;\n}\n\n.doctor-inner-box .doctor-profile .doctor-img:hover {\n  -webkit-transform: scale(1.1);\n  -moz-transform: scale(1.1);\n  -ms-transform: scale(1.1);\n  -o-transform: scale(1.1);\n  transform: scale(1.1);\n}\n\n.lh-35 {\n  line-height: 35px;\n}\n\n.doctor-info li {\n  margin-bottom: 10px;\n  color: #222;\n}\n\n.doctor-info li i {\n  margin-right: 20px;\n  color: #e12454;\n}\n\n.read-more {\n  color: #223a66;\n}\n\n@media (max-width: 480px) {\n  .doctors .btn-group {\n    display: block;\n  }\n  .doctors .btn-group .btn {\n    margin: 8px 3px;\n  }\n}\n\n@media (max-width: 400px) {\n  .doctors .btn-group {\n    display: block;\n  }\n  .doctors .btn-group .btn {\n    margin: 8px 3px;\n  }\n}\n\n@media (max-width: 768px) {\n  .doctors .btn-group {\n    display: block;\n  }\n  .doctors .btn-group .btn {\n    margin: 8px 3px;\n  }\n}\n\n.cta {\n  background: url(\"../images/bg/bg-4.jpg\") no-repeat 50% 50%;\n  background-size: cover;\n  position: relative;\n}\n\n.cta:before {\n  position: absolute;\n  content: \"\";\n  left: 0px;\n  top: 0px;\n  width: 100%;\n  height: 100%;\n  background: rgba(34, 58, 102, 0.95);\n}\n\n.mb-30 {\n  margin-bottom: 30px;\n}\n\n.text-color-primary {\n  color: #223a66;\n}\n\n.cta-section {\n  margin-bottom: -80px;\n}\n\n.cta-2 {\n  background: url(\"../images/bg/cta-bg.png\") no-repeat;\n  background-position: center center;\n}\n\n.cta-page {\n  background: url(\"../images/bg/banner.jpg\") no-repeat;\n  background-size: cover;\n  position: relative;\n}\n\n.testimonial {\n  position: relative;\n}\n\n.testimonial:before {\n  width: 48%;\n  height: 100%;\n  top: 0;\n  left: 0px;\n  position: absolute;\n  content: \"\";\n  background: url(\"../images/bg/bg-2.jpg\") no-repeat 50% 50%;\n}\n\n.testimonial .slick-dots {\n  text-align: left;\n}\n\n.testimonial-block {\n  position: relative;\n  margin-bottom: 20px;\n}\n\n.testimonial-block p {\n  background: #fff;\n  font-size: 18px;\n}\n\n.testimonial-block .client-info {\n  margin-bottom: 20px;\n}\n\n.testimonial-block .client-info h4 {\n  margin-bottom: 0px;\n}\n\n.testimonial-block i {\n  font-size: 60px;\n  position: absolute;\n  right: 46px;\n  bottom: 89px;\n  opacity: .08;\n}\n\n.testimonial-block .slick-dots {\n  text-align: left;\n}\n\n.testimonial-wrap-2 .slick-dots {\n  margin-left: -10px;\n}\n\n.testimonial-block.style-2 {\n  background: #fff;\n  padding: 30px;\n  margin: 0px 4px;\n  margin-bottom: 30px;\n}\n\n.testimonial-block.style-2 .testimonial-thumb {\n  float: left;\n}\n\n.testimonial-block.style-2 .testimonial-thumb img {\n  width: 80px;\n  height: 80px;\n  border-radius: 100%;\n  margin-right: 20px;\n  margin-bottom: 30px;\n  border: 5px solid #eff0f3;\n  margin-top: -5px;\n}\n\n.testimonial-block.style-2 .client-info p {\n  clear: both;\n  background: transparent;\n}\n\n.testimonial-block.style-2 i {\n  bottom: -20px;\n  color: #e12454;\n  opacity: .3;\n}\n\n@media (max-width: 480px) {\n  .testimonial-wrap {\n    margin-left: 0px;\n  }\n  .testimonial::before {\n    display: none;\n  }\n}\n\n@media (max-width: 400px) {\n  .testimonial-wrap {\n    margin-left: 0px;\n  }\n  .testimonial::before {\n    display: none;\n  }\n}\n\n@media (max-width: 768px) {\n  .testimonial-wrap {\n    margin-left: 0px;\n  }\n  .testimonial::before {\n    display: none;\n  }\n}\n\n@media (max-width: 992px) {\n  .testimonial-wrap {\n    margin-left: 0px;\n  }\n  .testimonial::before {\n    display: none;\n  }\n}\n\n.contact-form-wrap .form-group {\n  margin-bottom: 20px;\n}\n\n.contact-form-wrap .form-group .form-control {\n  height: 60px;\n  border: 1px solid #EEF2F6;\n  box-shadow: none;\n  width: 100%;\n  background: #f4f9fc;\n}\n\n.contact-form-wrap .form-group-2 {\n  margin-bottom: 13px;\n}\n\n.contact-form-wrap .form-group-2 textarea {\n  height: auto;\n  border: 1px solid #EEF2F6;\n  box-shadow: none;\n  background: #f4f9fc;\n  width: 100%;\n}\n\n.social-icons li {\n  margin: 0 6px;\n}\n\n.social-icons a {\n  margin-right: 10px;\n  font-size: 18px;\n}\n\n.google-map {\n  position: relative;\n}\n\n.google-map #map {\n  width: 100%;\n  height: 500px;\n}\n\n.mt-90 {\n  margin-top: 90px;\n}\n\n.contact-block {\n  text-align: center;\n  border: 5px solid #EEF2F6;\n  padding: 50px 25px;\n}\n\n.contact-block i {\n  font-size: 50px;\n  margin-bottom: 15px;\n  display: inline-block;\n  color: #e12454;\n}\n\n.blog-item-content h2 {\n  font-weight: 600;\n  font-size: 38px;\n}\n\n/*=================================================================\n  Single Blog Page\n==================================================================*/\n.nav-links .page-numbers {\n  display: inline-block;\n  width: 50px;\n  height: 50px;\n  border-radius: 100%;\n  background: #eee;\n  text-align: center;\n  padding-top: 13px;\n  font-weight: 600;\n  margin-right: 10px;\n}\n\n.nav-links .page-numbers:hover {\n  background: #223a66;\n  color: #fff;\n}\n\n.nav-links .page-numbers.current {\n  background: #223a66;\n  color: #fff;\n}\n\n.comment-area .comment-thumb {\n  margin-right: 20px;\n  margin-bottom: 30px;\n}\n\n.comment-area h5 {\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.comment-area span {\n  font-size: 14px;\n}\n\n.posts-nav h6 {\n  font-weight: 500;\n}\n\n.quote {\n  font-size: 22px;\n  color: #223a66;\n  padding: 40px;\n  font-style: italic;\n  border-left: 5px solid #e12454;\n  margin: 25px 0px;\n}\n\n.tag-option a {\n  border: 1px solid #eff0f3;\n  padding: 6px 12px;\n  color: #6F8BA4;\n  font-size: 14px;\n}\n\n.comment-form .form-control {\n  background: #f7f8fb;\n  border-radius: 5px;\n  border-color: #f7f8fb;\n  height: 50px;\n}\n\n.comment-form textarea.form-control {\n  height: auto;\n}\n\n.post.post-single {\n  border: none;\n}\n\n.post.post-single .post-thumb {\n  margin-top: 30px;\n}\n\n.post-sub-heading {\n  border-bottom: 1px solid #dedede;\n  padding-bottom: 20px;\n  letter-spacing: 2px;\n  text-transform: uppercase;\n  font-size: 16px;\n  margin-bottom: 20px;\n}\n\n.post-social-share {\n  margin-bottom: 50px;\n}\n\n.post-comments {\n  margin: 30px 0;\n}\n\n.post-comments .media {\n  margin-top: 20px;\n}\n\n.post-comments .media > .pull-left {\n  padding-right: 20px;\n}\n\n.post-comments .comment-author {\n  margin-top: 0;\n  margin-bottom: 0px;\n  font-weight: 500;\n}\n\n.post-comments .comment-author a {\n  color: #223a66;\n  font-size: 14px;\n  text-transform: uppercase;\n}\n\n.post-comments time {\n  margin: 0 0 5px;\n  display: inline-block;\n  color: #808080;\n  font-size: 12px;\n}\n\n.post-comments .comment-button {\n  color: #223a66;\n  display: inline-block;\n  margin-left: 5px;\n  font-size: 12px;\n}\n\n.post-comments .comment-button i {\n  margin-right: 5px;\n  display: inline-block;\n}\n\n.post-comments .comment-button:hover {\n  color: #223a66;\n}\n\n.post-excerpt {\n  margin-bottom: 60px;\n}\n\n.post-excerpt h3 a {\n  color: #000;\n}\n\n.post-excerpt p {\n  margin: 0 0 30px;\n}\n\n.post-excerpt blockquote.quote-post {\n  margin: 20px 0;\n}\n\n.post-excerpt blockquote.quote-post p {\n  line-height: 30px;\n  font-size: 20px;\n  color: #223a66;\n}\n\n.comments-section {\n  margin-top: 35px;\n}\n\n.author-about {\n  margin-top: 40px;\n}\n\n.post-author {\n  margin-right: 20px;\n}\n\n.post-author > img {\n  border: 1px solid #dedede;\n  max-width: 120px;\n  padding: 5px;\n  width: 100%;\n}\n\n.comment-list ul {\n  margin-top: 20px;\n}\n\n.comment-list ul li {\n  margin-bottom: 20px;\n}\n\n.comment-wrap {\n  border: 1px solid #dedede;\n  border-radius: 1px;\n  margin-left: 20px;\n  padding: 10px;\n  position: relative;\n}\n\n.comment-wrap .author-avatar {\n  margin-right: 10px;\n}\n\n.comment-wrap .media .media-heading {\n  font-size: 14px;\n  margin-bottom: 8px;\n}\n\n.comment-wrap .media .media-heading a {\n  color: #223a66;\n  font-size: 13px;\n}\n\n.comment-wrap .media .comment-meta {\n  font-size: 12px;\n  color: #888;\n}\n\n.comment-wrap .media p {\n  margin-top: 15px;\n}\n\n.comment-reply-form {\n  margin-top: 80px;\n}\n\n.comment-reply-form input, .comment-reply-form textarea {\n  height: 35px;\n  border-radius: 0;\n  box-shadow: none;\n}\n\n.comment-reply-form input:focus, .comment-reply-form textarea:focus {\n  box-shadow: none;\n  border: 1px solid #223a66;\n}\n\n.comment-reply-form textarea, .comment-reply-form .btn-main {\n  height: auto;\n}\n\n.sidebar-widget {\n  margin-bottom: 30px;\n  padding-bottom: 35px;\n}\n\n.sidebar-widget h5 {\n  margin-bottom: 30px;\n  position: relative;\n  padding-bottom: 15px;\n}\n\n.sidebar-widget h5:before {\n  position: absolute;\n  content: \"\";\n  left: 0px;\n  bottom: 0px;\n  width: 35px;\n  height: 3px;\n  background: #e12454;\n}\n\n.sidebar-widget.latest-post .media img {\n  border-radius: 7px;\n}\n\n.sidebar-widget.latest-post .media h6 {\n  font-weight: 500;\n  line-height: 1.4;\n}\n\n.sidebar-widget.latest-post .media p {\n  font-size: 12px;\n}\n\n.sidebar-widget.category ul li {\n  margin-bottom: 10px;\n}\n\n.sidebar-widget.category ul li a {\n  color: #222;\n  -webkit-transition: all 0.3s ease;\n  -moz-transition: all 0.3s ease;\n  -ms-transition: all 0.3s ease;\n  -o-transition: all 0.3s ease;\n  transition: all 0.3s ease;\n}\n\n.sidebar-widget.category ul li a:hover {\n  color: #223a66;\n  padding-left: 5px;\n}\n\n.sidebar-widget.category ul li span {\n  margin-left: 10px;\n}\n\n.sidebar-widget.tags a {\n  font-size: 12px;\n  text-transform: uppercase;\n  letter-spacing: .075em;\n  line-height: 41px;\n  height: 41px;\n  font-weight: 500;\n  border-radius: 20px;\n  color: #666;\n  display: inline-block;\n  background-color: #eff0f3;\n  margin: 0 7px 10px 0;\n  padding: 0 25px;\n  -webkit-transition: all .2s ease;\n  -moz-transition: all .2s ease;\n  transition: all .2s ease;\n}\n\n.sidebar-widget.tags a:hover {\n  color: #fff;\n  background: #223a66;\n}\n\n.sidebar-widget.schedule-widget {\n  background: #f4f9fc;\n  padding: 25px;\n}\n\n.sidebar-widget.schedule-widget ul li {\n  padding: 10px 0px;\n  border-bottom: 1px solid #eee;\n}\n\n.search-form {\n  position: relative;\n}\n\n.search-form i {\n  position: absolute;\n  right: 15px;\n  top: 35%;\n}\n\n.footer {\n  padding-bottom: 10px;\n}\n\n.footer .copyright a {\n  font-weight: 600;\n}\n\n.lh-35 {\n  line-height: 35px;\n}\n\n.logo {\n  font-weight: 600;\n  letter-spacing: 1px;\n}\n\n.logo h3 {\n  color: #223a66;\n}\n\n.logo span {\n  color: #223a66;\n}\n\n.widget .divider {\n  height: 3px;\n}\n\n.widget h4 {\n  color: #223a66;\n}\n\n.widget .footer-menu a {\n  color: #6F8BA4;\n}\n\n.widget .footer-menu a:hover {\n  color: #e12454;\n}\n\n.footer-contact-block span {\n  font-weight: 400;\n  color: #6F8BA4;\n}\n\n.footer-contact-block i {\n  font-size: 20px;\n}\n\n.footer-btm {\n  border-top: 1px solid rgba(0, 0, 0, 0.06);\n}\n\n.footer-socials li a {\n  width: 45px;\n  height: 45px;\n  background: #6F8BA4;\n  color: #fff;\n  display: inline-block;\n  text-align: center;\n  border-radius: 100%;\n  padding-top: 12px;\n}\n\n.widget-contact h6 {\n  font-weight: 500;\n  margin-bottom: 18px;\n}\n\n.widget-contact h6 i {\n  color: #e12454;\n}\n\n.subscribe {\n  position: relative;\n}\n\n.subscribe .form-control {\n  border-radius: 50px;\n  height: 60px;\n  padding-left: 25px;\n  border-color: #eee;\n}\n\n.subscribe .btn {\n  position: absolute;\n  right: 6px;\n  top: 6px;\n}\n\n.backtop {\n  position: fixed;\n  background: #e12454;\n  z-index: 9999;\n  display: inline-block;\n  right: 55px;\n  width: 60px;\n  height: 60px;\n  bottom: 50px;\n  text-align: center;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  opacity: 0;\n  border-radius: 50px;\n}\n\n.backtop i {\n  color: #fff;\n  font-size: 20px;\n}\n\n.reveal {\n  transition: all .3s;\n  cursor: pointer;\n  opacity: 1;\n}\n", "\n\n#navbarmain{\n padding: 20px 0px;\n\n .nav-link{\n    font-weight: 600;\n    padding: 10px 15px;\n    color: $black;\n    font-family: $primary-font;\n    text-transform: capitalize;\n    font-size: 16px;\n    -webkit-transition: all .25s ease;\n       -moz-transition: all .25s ease;\n        -ms-transition: all .25s ease;\n         -o-transition: all .25s ease;\n            transition: all .25s ease;\n   \n  }\n\n}\n\n.dropdown-toggle::after {\n    display: none;\n}\n\n.navbar-brand{\n  margin-top: 10px;\n}\n\n\n.dropdown {\n\n  .dropdown-menu{\n    position: absolute;\n    display: block;\n    background:$light;\n    min-width: 240px;\n    top: 130%;\n    left: 0;\n    right:0px;\n    opacity: 0;\n    padding: 0px;\n    visibility: hidden;\n    -webkit-transition: all 0.3s ease-out 0s;\n    -moz-transition: all 0.3s ease-out 0s;\n    -ms-transition: all 0.3s ease-out 0s;\n    -o-transition: all 0.3s ease-out 0s;\n    transition: all 0.3s ease-out 0s;\n    border:0px;\n    border-top: 5px solid #e12454;\n    border-radius: 0px;\n  }\n\n   &:hover .dropdown-menu{\n      opacity: 1;\n      visibility: visible;\n      top:115%;\n    }\n\n    .dropdown-item{\n      padding: 13px 20px;\n      border-bottom:1px solid #eee;\n      background: transparent;\n      font-weight: 400;\n      color: #555;\n      &:hover{\n        color: $secondary-color;\n      }\n    }\n}\n\n\n\n\n\n\n\n\n\n\n.header-top-bar{\n  background: $primary-color;\n  font-size: 14px;\n  padding:10px 0px;\n  box-shadow: 0 1px 2px rgba(0,0,0,.05);\n  color: $light;\n}\n\n.top-bar-info{\n  li{\n    a{\n      color: $light;\n      margin-right:20px;\n    }\n  }\n}\n\n.top-right-bar{\n  a{\n      span{\n        color: $light;\n        font-weight: 600;\n        letter-spacing: 1px;\n      }\n      i{\n        // width: 40px;\n        // height:40px;\n        // display: inline-block;\n        // background: $base-color;\n        // border-radius: 100%;\n        // text-align: center;\n        // padding-top: 13px;\n        color: $light;\n        margin-right: 10px;\n      }\n    }\n}\n\n// .navigation{\n//   position: absolute;\n//   top:0px;\n//   left: 0px;\n//   width: 100%;\n//   z-index: 99999;\n\n//   .btn{\n//     box-shadow: none;\n//   }\n// }\n\n// #navbar{\n//   box-shadow: 0 1px 2px rgba(0,0,0,.05);\n\n//  .nav-link{\n//     padding-left: 26px;\n//     font-weight: 500;\n//     color: $black;\n//     font-family: $primary-font;\n//     text-transform: capitalize;\n//     font-size: 18px;\n//     -webkit-transition: all .25s ease;\n//        -moz-transition: all .25s ease;\n//         -ms-transition: all .25s ease;\n//          -o-transition: all .25s ease;\n//             transition: all .25s ease;\n   \n//   }\n\n\n// }\n\n// #navbar.nav-text-white{\n//   .nav-link{\n//      color: $light;\n//   }\n\n//   .navbar-brand{\n//      color: $light;\n//   }\n// }\n\n\n// .navbar-brand{\n//   font-weight: 600;\n \n// }\n\n// .dropdown-menu{\n//   visibility: hidden;\n//   filter: alpha(opacity=0);\n//   opacity: 0;\n//   transition:all .2s ease-in, visibility 0s linear .2s, transform .2s linear;\n//   -webkit-transition: all .2s ease-in, visibility 0s linear .2s, -webkit-transform .2s linear;\n//   -o-transition: all .2s ease-in, visibility 0s linear .2s, -o-transform .2s linear;\n//   -ms-transition: all .2s ease-in, visibility 0s linear .2s, -ms-transform .2s linear;\n//   width: 250px;\n//   margin-top: 15px;\n//   padding: 0px;\n//   border-radius: 0px;\n//   display: block;\n//   border: 1px solid rgba(0,0,0,.05);\n// }\n\n// .dropdown-toggle::after {\n//     display: none;\n// }\n\n// .dropdown:hover .dropdown-menu{\n//     visibility: visible;\n//     -webkit-transition: all .45s ease 0s;\n//        -moz-transition: all .45s ease 0s;\n//         -ms-transition: all .45s ease 0s;\n//          -o-transition: all .45s ease 0s;\n//             transition: all .45s ease 0s;\n//     opacity: 1;\n// }\n// .dropdown-item{\n//   padding: .8rem 1.5rem;\n//   text-transform: uppercase;\n//   font-size: 14px;\n// }\n\n// .dropdown-item:hover{\n//   background: $primary-color;\n//   color: $light;\n// }\n\n// ul.dropdown-menu li {\n//     padding-left: 0px!important;\n// }\n//   #navbar.nav-text-white .mobile-logo{\n//       display: none;\n//   }\n\n// // Responsive\n\n\n// @include mobile{\n//   #navbar{\n//     background: $light;\n//   } \n//   #navbar.nav-text-white{\n//     .nav-link{\n//       color: $black;\n//     }\n//   }\n\n//   #navbar.nav-text-white .mobile-logo{\n//       display: block;\n//   }\n  \n//   #navbar.nav-text-white .hidden-logo{\n//       display: none;\n//   }\n//  .navbar-toggler {\n//       color: $black;\n//   }\n//   .dropdown-menu{\n//     display: none;\n//     width: 100%;\n//     text-align: center;\n//   }\n\n//    h2, .h2 {\n//       font-size: 1.3rem;\n//       font-weight: 600;\n//       line-height: 36px;\n//   }\n//   .header-padding {\n//       padding: 0px 30px;\n//   }\n\n// }\n\n\n// @include mobile-xs{\n//   #navbar{\n//     background: $light;\n//   }\n//   #navbar.nav-text-white{\n//     .nav-link{\n//       color: $black;\n//     }\n//   }\n\n//    #navbar.nav-text-white .mobile-logo{\n//       display: block;\n//   }\n  \n//   #navbar.nav-text-white .hidden-logo{\n//       display: none;\n//   }\n//   .navbar-toggler {\n//       color: $black;\n//   }\n\n//   .dropdown-menu{\n//     display: none;\n//     width: 100%;\n//     text-align: center;\n//   }\n//    .header-padding {\n//       padding: 0px 30px;\n//   }\n\n// }\n\n\n// @include tablet{\n//   #navbar{\n//     background: $light;\n//   }\n//   #navbar.nav-text-white{\n//     .nav-link{\n//       color: $black;\n//     }\n//   }\n\n  \n//   .navbar-toggler {\n//       color: $black;\n//   }\n\n//   .dropdown-menu{\n//     display: none;\n//     width: 100%;\n//     text-align: center;\n//   }\n//    #navbar.nav-text-white .mobile-logo{\n//       display: block;\n//   }\n  \n//   #navbar.nav-text-white .hidden-logo{\n//       display: none;\n//   }\n//    .header-padding {\n//       padding: 0px 30px;\n//   }\n\n// }\n\n\n\n// @include desktop{\n//   #navbar{\n//     background: $light;\n//   }\n//    #navbar.nav-text-white{\n//     .nav-link{\n//       color: $black;\n//     }\n//   }\n\n\n//   #navbar.nav-text-white .mobile-logo{\n//       display: block;\n//   }\n  \n//   #navbar.nav-text-white .hidden-logo{\n//       display: none;\n//   }\n\n//  .navbar-toggler {\n//       color: $black;\n//   }\n// .dropdown-menu{\n//     display: none;\n//     width: 100%;\n//     text-align: center;\n//   }\n//    .header-padding {\n//       padding: 0px 30px;\n//   }\n// }\n\n\n\n\n", ".bg-1 {\n\tbackground: url(\"../images/bg/22.jpg\") no-repeat 50% 50%;\n\tbackground-size: cover;\n  \tposition: relative;\n}\n\n", ".banner{\n  position: relative;\n  overflow: hidden;\n  background: $light;\n  background: url(\"../images/bg/slider-bg-1.jpg\") no-repeat;\n  background-size: cover;\n  min-height: 550px;\n\n  .block{\n    padding: 80px 0px 160px;\n\n    h1{\n      font-size: 60px;\n      line-height: 1.2;\n      letter-spacing:-1.2px;\n      text-transform: capitalize;\n      color: $title-color;\n    }\n\n  }\n}\n\n.letter-spacing{\n  letter-spacing:2px;\n}\n\n\n.text-color{\n  color: $primary-color;\n}\n\n.text-color-2{\n  color: $secondary-color;\n}\n\n\n.divider{\n  width: 40px;\n  height:5px;\n  background: $secondary-color;\n}\n\n\n\n\n\n\n\n\n\n\n\n@include mobile{\n   .banner .block h1 {\n      font-size: 38px;\n      line-height: 50px;\n  }\n\n  .banner{\n    min-height: 450px;\n    background: $light!important;\n  }\n}\n\n\n@include mobile-xs{\n    .banner .block h1 {\n      font-size: 28px;\n      line-height: 40px;\n  }\n\n  .banner{\n     min-height: 450px;\n    background: $light!important;\n  }\n\n}\n\n\n@include tablet{\n     .banner .block h1 {\n        font-size: 56px;\n        line-height: 70px;\n    }\n\n    .banner{\n      background: $light!important;\n    }\n\n}\n\n@include desktop{\n    .banner{\n      background: $light!important;\n    }\n}\n\n@include large-desktop{\n    .banner{\n      // background: $light!important;\n    }\n}\n\n\n", "\n.about-img{\n\timg{\n\t\tborder-radius:5px;\n\t\tbox-shadow: 0px 0px 30px 0px rgba(0, 42, 106, 0.1);\n\t}\n}\n\n\n\n// Awards\n\n.award-img{\n\theight: 120px;\n\tmargin-bottom: 10px;\n\talign-items: center;\n\tdisplay: flex;\n\tjustify-content: center;\n\tbackground:$gray;\n}\n\n\n//  Appoinment\n\n\n.appoinment-content{\n\tposition: relative;\n\timg{\n\t\twidth: 85%;\n\t}\n\t.emergency{\n\t\tposition: absolute;\n\t\tcontent: \"\";\n\t\tright: 10px;\n\t\tbottom: 20px;\n\t\tbackground: $primary-color;\n\t\tpadding: 48px;\n\n\t\th2{\n\t\t\tcolor: $light;\n\t\t}\n\n\t\ti{\n\t\t\tmargin-right: 10px;\n\t\t\tcolor:rgba(255,255,255,.7);\n\t\t}\n\t}\n}\n\n\n.appoinment-form{\n\tmargin-top: 40px;\n\t.form-control{\n\t\tbackground: #f4f9fc;\n\t\theight: 55px;\n\t\tborder-color:rgba(0,0,0,0.05);\n\t}\n\n\ttextarea.form-control{\n\t\theight:auto;\n\t}\n}\n\n.client-thumb{\n\ttext-align: center;\n}", "\n\n// Home feature\n\n.features{\n\tmargin-top: -70px;\n}\n.feature-item{\n\tflex-basis: 33.33%;\n\tmargin: 0px 10px;\n\tpadding: 40px 30px;\n\tbackground-color: $light;\n\tborder-radius: 15px 15px 15px 15px;\n\tbox-shadow: 0px 0px 30px 0px rgba(0,42,106,0.1);\n\t.feature-icon{\n\t\ti{\n\t\t\tfont-size: 50px;\n\t\t\tcolor: $primary-color;\n\t\t}\n\t}\n\n\th4{\n\t\tcolor: $primary-color;\n\t}\n\n\tp{\n\t\tfont-size: 14px;\n\t}\n\n\n}\n\n.feature-section.border-top{\n\tborder-top:1px solid rgba(0,0,0,0.05)!important;\n}\n\n.w-hours{\n\tli{\n\t\tpadding: 6px 0px;\n\t\tborder-bottom:1px solid rgba(0,0,0,0.05);\n\t}\n}\n\n", ".counter-stat{\n\ttext-align: center;\n\tpadding: 55px 0px 40px 0px;\n\tposition: relative;\n\ti{\n\t\tdisplay: block;\n\t\tcolor: rgba(255,255,255,.06);\n\t\tfont-size: 70px;\n\t\tposition: absolute;\n\t\tleft: 0px;\n\t\tright:0px;\n\t\ttop:0px;\n\t\t-webkit-transform: translateY(25px);\n\t\t   -moz-transform: translateY(25px);\n\t\t    -ms-transform: translateY(25px);\n\t\t     -o-transform: translateY(25px);\n\t\t        transform: translateY(25px);\n\t}\n\n\tspan{\n\t\tfont-size: 70px;\n\t\tcolor: $light;\n\t}\n\n\n\tp{\n\t\tmargin-bottom: 0px;\n\t\tcolor: rgba(255,255,255,.7);\n\t}\n}\n\n.mb--80{\n\tmargin-bottom: -80px;\n}\n\n\n\n\n\n\n\n", ".service{\n\tpadding-top:180px;\n\n\t.service-item{\n\t\tbackground: $light;\n\t\tpadding: 30px;\n\t\tborder-radius:5px;\n\t}\n\n\t.icon{\n\t\tfloat: left;\n\t\tmargin-bottom: 10px;\n\t}\n\ti{\n\t\tcolor: $secondary-color;\n\t}\n\th4{\n\t\tpadding-left: 20px;\n\t}\n\n\t.content{\n\t\tclear: both;\n\t}\n}\n\n//  Service-2\n\n\n.service-block{\n\tpadding: 20px;\n\tmargin-top: 40px;\n\tborder:1px solid rgba(0,0,0,0.03);\n\tbox-shadow: 0 0 38px rgba(21,40,82,.07);\n\n\timg{\n\t\twidth: 100%;\n\t\tmargin-top: -60px;\n\t\tborder:5px solid $light;\n\t}\n\n}\n\n", ".department-service{\n\tmargin-bottom: 40px;\n\tli{\n\t\tmargin-bottom: 10px;\n\n\t\ti{\n\t\t\tcolor: $secondary-color;\n\t\t}\n\t}\n}\n\n//  Doctors\n\n\n\n.doctors{\n\t.btn-group{\n\t\t.btn{\n\t\t\tborder-radius: 0px;\n\t\t\tmargin: 0px 2px;\n\t\t\ttext-transform: capitalize;\n\t\t\tfont-size: 16px;\n\t\t\tpadding: .6rem 1.5rem;\n\t\t\tcursor: pointer;\n\n\t\t\t&.active{\n\t\t\t\tbox-shadow: none!important;\n\t\t\t\tborder-color:transparent;\n\t\t\t\tbackground: $secondary-color;\n\t\t\t\tcolor: $light;\n\t\t\t}\n\n\t\t\t&.focus{\n\t\t\t\tbox-shadow: none!important;\n\t\t\t\tborder-color:transparent;\n\t\t\t}\n\t\t\t&:focus{\n\t\t\t\tbox-shadow: none!important;\n\t\t\t\tborder-color:transparent;\n\t\t\t\tbackground: $secondary-color;\n\t\t\t\tcolor: $light;\n\t\t\t}\n\n\t\t\t&:hover{\n\t\t\t\tbox-shadow: none!important;\n\t\t\t\tborder-color:transparent;\n\t\t\t\tbackground: $secondary-color;\n\t\t\t\tcolor: $light;\n\t\t\t}\n\n\t\t}\n\t}\n\n\t.btn-group > .btn-group:not(:last-child) > .btn, .btn-group > .btn:not(:last-child):not(.dropdown-toggle),.btn-group > .btn:not(:first-child) {\n\t    border-radius: 3px;\n\t}\n}\n\n// Doctors\n\n.doctor-inner-box{\n\toverflow: hidden;\n\t.doctor-profile{\n\t\toverflow: hidden;\n\t\tposition: relative;\n\t\tbox-shadow: 0px 8px 16px 0px rgba(200, 183, 255, 0.2);\n\n\t\t.doctor-img{\n\t\t\t-webkit-transition: all .35s ease;\n\t\t   -moz-transition: all .35s ease;\n\t\t    -ms-transition: all .35s ease;\n\t\t     -o-transition: all .35s ease;\n\t\t        transition: all .35s ease;\n\n\t\t\t&:hover{\n\t\t\t\t-webkit-transform: scale(1.1);\n\t\t\t\t   -moz-transform: scale(1.1);\n\t\t\t\t    -ms-transform: scale(1.1);\n\t\t\t\t     -o-transform: scale(1.1);\n\t\t\t\t        transform: scale(1.1);\n\t\t\t}\n\t\t}\n\t}\n}\n\n.lh-35{\n\tline-height: 35px;\n}\n\n\n\n// Doctors info\n\n\n.doctor-info{\n\tli {\n\t\tmargin-bottom: 10px;\n\t\tcolor: $black;\n\t\ti{\n\t\t\tmargin-right: 20px;\n\t\t\tcolor: $secondary-color;\n\t\t}\n\t}\n}\n\n.read-more{\n\tcolor: $primary-color;\n}\n\n\n@include mobile{\n\t.doctors{\n\t\t.btn-group{\n\t\t\tdisplay: block;\n\t\t\t.btn{\n\t\t\t\tmargin: 8px 3px;\n\t\t\t}\n\t\t}\n\t\t\n\t}\n}\n\n@include mobile-xs{\n\t.doctors{\n\t\t.btn-group{\n\t\t\tdisplay: block;\n\t\t\t.btn{\n\t\t\t\tmargin: 8px 3px;\n\t\t\t}\n\t\t}\n\t\t\n\t}\n}\n\n@include tablet{\n\t.doctors{\n\t\t.btn-group{\n\t\t\tdisplay: block;\n\t\t\t.btn{\n\t\t\t\tmargin: 8px 3px;\n\t\t\t}\n\t\t}\n\t\t\n\t}\n}", ".cta{\n\tbackground: url(\"../images/bg/bg-4.jpg\")no-repeat 50% 50%;\n\tbackground-size: cover;\n\tposition: relative;\n\n\t&:before{\n\t\tposition: absolute;\n\t\tcontent: \"\";\n\t\tleft: 0px;\n\t\ttop:0px;\n\t\twidth:100%;\n\t\theight:100%;\n\t\tbackground: rgba(34, 58, 102,.95);\n\t}\n}\n\n.mb-30{\n\tmargin-bottom: 30px;\n}\n\n.text-color-primary{\n\tcolor: $primary-color;\n}\n\n.cta-section{\n\tmargin-bottom: -80px;\n}\n\n//  CTA -2 \n//  \n.cta-2{\n\tbackground: url(\"../images/bg/cta-bg.png\") no-repeat;\n\tbackground-position: center center;\n}\n\n\n.cta-page{\n\tbackground: url(\"../images/bg/banner.jpg\")no-repeat;\n\tbackground-size: cover;\n\tposition: relative;\n}\n", ".testimonial{\n\tposition: relative;\n\t&:before{\n\t\twidth: 48%;\n\t\theight:100%;\n\t\ttop: 0;\n\t\tleft:0px;\n\t\tposition:absolute;\n\t\tcontent:\"\";\n\t\tbackground: url(\"../images/bg/bg-2.jpg\") no-repeat 50% 50%;\n\t}\n\n\t.slick-dots{\n\t\ttext-align: left;\n\t}\n}\n.testimonial-block{\n\tposition: relative;\n\tmargin-bottom: 20px;\n\n\tp{\n\t\tbackground: $light;\n\t\tfont-size: 18px;\n\n\t}\n\n\t.client-info{\n\t\tmargin-bottom: 20px;\n\n\t\th4{\n\t\t\tmargin-bottom: 0px;\n\t\t}\n\t}\n\n\ti{\n\t\tfont-size: 60px;\n\t\tposition: absolute;\n\t\tright: 46px;\n\t\tbottom: 89px;\n\t\topacity: .08;\n\t}\n\n\t.slick-dots{\n\t\ttext-align: left;\n\t}\n}\n\n\n\n// Home Testimnial\n\n.testimonial-wrap-2{\n\t.slick-dots{\n\t\tmargin-left: -10px;\n\t}\n}\n.testimonial-block.style-2{\n\tbackground: $light;\n\tpadding: 30px;\n\tmargin: 0px 4px;\n\tmargin-bottom: 30px;\n\n\t.testimonial-thumb{\n\t\tfloat: left;\n\n\t\timg{\n\t\t\twidth: 80px;\n\t\t\theight:80px;\n\t\t\tborder-radius:100%;\n\t\t\tmargin-right: 20px;\n\t\t\tmargin-bottom: 30px;\n\t\t\tborder:5px solid $gray;\n\t\t\tmargin-top: -5px;\n\t\t}\n\t}\n\n\t.client-info p{\n\t\tclear: both;\n\t\tbackground: transparent;\n\t}\n\n\ti {\n\t    bottom: -20px;\n\t    color: $secondary-color;\n\t    opacity: .3;\n\t}\n\n}\n\n\n\n\n\n\n\n@include mobile{\n\t.testimonial-wrap{\n\t\tmargin-left: 0px;\n\t}\n\n\t.testimonial::before{\n\t\tdisplay: none;\n\t}\n}\n@include mobile-xs{\n\t.testimonial-wrap{\n\t\tmargin-left: 0px;\n\t}\n\t.testimonial::before{\n\t\tdisplay: none;\n\t}\n}\n@include tablet{\n\t.testimonial-wrap{\n\t\tmargin-left: 0px;\n\t}\n\t.testimonial::before{\n\t\tdisplay: none;\n\t}\n}\n@include desktop{\n\t.testimonial-wrap{\n\t\tmargin-left: 0px;\n\t}\n\t.testimonial::before{\n\t\tdisplay: none;\n\t}\n}\n", ".contact-form-wrap{\n      .form-group{\n       margin-bottom: 20px;\n        .form-control{\n          height:60px;\n          border: 1px solid #EEF2F6;\n          box-shadow: none;\n          width: 100%;\n          background: #f4f9fc;\n        }\n      }\n      .form-group-2{\n        margin-bottom: 13px;\n        textarea{\n          height:auto;\n          border: 1px solid #EEF2F6;\n          box-shadow: none;\n          background: #f4f9fc;\n          width: 100%;\n        }\n      }\n}\n\n\n.social-icons {\n  li {\n    margin:0 6px;\n  }\n \n  a{\n    margin-right: 10px;\n    font-size: 18px;\n  }\n}\n\n.google-map {\n    position: relative;\n}\n\n.google-map #map {\n    width: 100%;\n    height: 500px;\n}\n\n\n\n.mt-90{\n  margin-top: 90px;\n}\n\n.contact-block{\n  text-align: center;\n  border:5px solid #EEF2F6;\n  padding:50px 25px;\n  i{\n    font-size: 50px;\n    margin-bottom: 15px;\n    display: inline-block;\n    color: $secondary-color;\n  }\n}", ".blog-item-content{\n\th2{\n\t\tfont-weight: 600;\n\t\tfont-size: 38px;\n\t}\n}\n", "/*=================================================================\n  Single Blog Page\n==================================================================*/\n\n.nav-links{\n  .page-numbers{\n    display: inline-block;\n    width: 50px;\n    height: 50px;\n    border-radius: 100%;\n    background: #eee;\n    text-align: center;\n    padding-top: 13px;\n    font-weight: 600;\n    margin-right: 10px;\n\n    &:hover{\n      background: $primary-color;\n      color: $light;\n    }\n    &.current{\n      background: $primary-color;\n      color: $light;\n    }\n  }\n\n\n}\n\n//  Comments\n\n.comment-area{\n\n  .comment-thumb{\n    margin-right: 20px;\n    margin-bottom: 30px;\n  }\n  \n  h5{\n    font-size: 18px;\n    font-weight: 500;\n  }\n  span{\n    font-size: 14px;\n  }\n}\n\n\n.posts-nav {\n  h6{\n    font-weight: 500;\n  }\n}\n\n\n\n\n.quote{\n  font-size: 22px;\n  color: $title-color;\n  padding: 40px;\n  font-style: italic;\n  border-left:5px solid $secondary-color;\n  margin: 25px 0px;\n}\n\n.tag-option{\n  a{\n    border:1px solid $gray;\n    padding: 6px 12px;\n    color: $base-color;\n    font-size: 14px;\n  }\n}\n\n\n\n\n\n\n\n// Comment Form\n.comment-form {\n  .form-control{\n    background: #f7f8fb;\n    border-radius:5px;\n    border-color:#f7f8fb;\n    height: 50px;\n  }\n\n  textarea.form-control{\n    height:auto;\n  }\n}\n\n\n\n\n\n\n\n\n\n\n\n\n.post.post-single {\n  border:none;\n  .post-thumb {\n    margin-top:30px;\n  }\n}\n.post-sub-heading {\n  border-bottom:1px solid #dedede;\n  padding-bottom:20px;\n  letter-spacing: 2px;\n  text-transform: uppercase;\n  font-size: 16px;\n  margin-bottom:20px;\n}\n.post-social-share {\n  margin-bottom:50px;\n}\n\n.post-comments {\n  margin:30px 0;\n  .media {\n    margin-top:20px;\n    > .pull-left {\n      padding-right: 20px;\n    }\n  }\n  .comment-author {\n    margin-top: 0;\n    margin-bottom:0px;\n    font-weight:500;\n    a {\n      color: $primary-color;\n      font-size: 14px;\n      text-transform: uppercase;\n    }\n  }\n  time {\n    margin:0 0 5px;\n    display: inline-block;\n    color: #808080;\n    font-size:12px;\n  }\n  .comment-button {\n    color: $primary-color;\n    display: inline-block;\n    margin-left:5px;\n    font-size:12px;\n    i {\n      margin-right:5px;\n      display: inline-block;\n    }\n    &:hover {\n      color: $primary-color;\n    }\n  }\n}\n\n.post-excerpt {\n  margin-bottom: 60px;\n  h3 {\n    a {\n      color: #000;\n    }\n  }\n  p {\n    margin: 0 0 30px;\n  }\n  blockquote.quote-post {\n    margin: 20px 0;\n    p {\n      line-height: 30px;\n      font-size: 20px;\n      color:$primary-color;\n    }\n  }\n}\n\n\n\n.comments-section {\n  margin-top: 35px;\n}\n\n\n.author-about {\n  margin-top: 40px;\n}\n.post-author {\n  margin-right: 20px;\n}\n\n.post-author > img {\n  border: 1px solid #dedede;\n  max-width: 120px;\n  padding: 5px;\n  width: 100%;\n}\n\n\n\n.comment-list {\n  ul {\n    margin-top: 20px;\n    li {\n      margin-bottom: 20px;\n    }\n  }\n}\n\n\n.comment-wrap {\n  border: 1px solid #dedede;\n  border-radius: 1px;\n  margin-left: 20px;\n  padding: 10px;\n  position: relative;\n  .author-avatar {\n    margin-right: 10px;\n  }\n  .media {\n    .media-heading {\n      font-size: 14px;\n      margin-bottom: 8px;\n      a {\n        color: $primary-color;\n        font-size: 13px;\n      }\n    }\n    .comment-meta {\n      font-size: 12px;\n      color: #888;\n    }\n    p {\n      margin-top: 15px;\n    }\n  }\n\n}\n\n\n.comment-reply-form {\n  margin-top: 80px;\n  input,textarea {\n    height: 35px;\n    border-radius: 0;\n    box-shadow: none;\n    &:focus {\n      box-shadow:none;\n      border:1px solid $primary-color;\n    }\n  }\n  textarea,.btn-main {\n    height: auto;\n  }\n}\n\n                            \n\n\n\n", "\n\n.sidebar-widget {\n  margin-bottom:30px;\n  padding-bottom:35px;\n\n  h5{\n    margin-bottom:30px;\n    position: relative;\n    padding-bottom: 15px;\n    &:before{\n      position: absolute;\n      content:\"\";\n      left: 0px;\n      bottom:0px;\n      width: 35px;\n      height: 3px;\n      background: $secondary-color;\n    }\n\n  }\n\n  // latest Posts\n  &.latest-post{\n    .media {\n      img{\n        border-radius:7px;\n      }\n      h6{\n        font-weight: 500;\n        line-height: 1.4;\n      }\n      p {\n        font-size: 12px;\n      }\n    }\n  } //end latest posts\n\n  // Caterogry\n  &.category {\n    ul {\n      li {\n        margin-bottom: 10px;\n        a {\n          color: $black;\n          @include transition (all, 0.3s, ease);\n          &:hover {\n            color:$primary-color;\n            padding-left: 5px;\n          }\n        }\n        span{\n          margin-left: 10px;\n        }\n      }\n    }\n  } //end caterogry\n\n\n &.tags{\n  a{\n    font-size: 12px;\n    text-transform: uppercase;\n    letter-spacing: .075em;\n    line-height: 41px;\n    height: 41px;\n    font-weight: 500;\n    border-radius: 20px;\n    color: #666;\n    display: inline-block;\n    background-color: #eff0f3;\n    margin: 0 7px 10px 0;\n    padding: 0 25px;\n    -webkit-transition: all .2s ease;\n    -moz-transition: all .2s ease;\n    transition: all .2s ease;\n\n    &:hover{\n      color: $light;\n      background: $primary-color;\n    }\n  }\n }\n\n&.schedule-widget{\n  background: #f4f9fc;\n  padding: 25px;\n  ul {\n      li{\n      padding: 10px 0px;\n      border-bottom:1px solid #eee;\n    }\n  }\n}\n\n\n}\n\n\n// Search\n// \n\n\n.search-form{\n  position: relative;\n  i{\n    position: absolute;\n    right: 15px;\n    top:35%;\n  }\n}", "// Transition\n@mixin transition($what: all, $time: 0.2s, $how: ease-in-out) {\n    -webkit-transition: $what $time $how;\n    -moz-transition:    $what $time $how;\n    -ms-transition:     $what $time $how;\n    -o-transition:      $what $time $how;\n    transition:         $what $time $how;\n}\n\n// Transform\n@mixin transform($transforms) {\n\t   -moz-transform: $transforms;\n\t     -o-transform: $transforms;\n\t    -ms-transform: $transforms;\n\t-webkit-transform: $transforms;\n          transform: $transforms;\n}\n// rotate\n@mixin rotate ($deg) {\n  @include transform(rotate(#{$deg}deg));\n}\n \n// scale\n@mixin scale($scale) {\n\t @include transform(scale($scale));\n} \n// translate\n@mixin translate ($x, $y) {\n   @include transform(translate($x, $y));\n}\n// skew\n@mixin skew ($x, $y) {\n   @include transform(skew(#{$x}deg, #{$y}deg));\n}\n//transform origin\n@mixin transform-origin ($origin) {\n    moz-transform-origin: $origin;\n\t     -o-transform-origin: $origin;\n\t    -ms-transform-origin: $origin;\n\t-webkit-transform-origin: $origin;\n          transform-origin: $origin;\n}", ".footer{\n padding-bottom: 10px;\n\n  .copyright{\n    a{\n      font-weight: 600;\n    }\n  }\n}\n\n\n.lh-35{\n  line-height: 35px;\n}\n\n\n.logo{\n  h3{\n    color: $primary-color;\n  }\n\n  font-weight: 600;\n  letter-spacing: 1px;\n\n  span{\n    color: $primary-color;\n  }\n}\n\n\n.widget{\n\n  .divider{\n    height:3px;\n  }\n\n  h4{\n      color: $primary-color;\n  }\n\n  .footer-menu a{\n    color: $base-color;\n\n    &:hover{\n      color: $secondary-color;\n    }\n  }\n \n}\n\n.footer-contact-block{\n  span{\n    font-weight: 400;\n    color: $base-color;\n  }\n  i{\n    font-size: 20px;\n  }\n \n}\n\n.footer-btm{\n  border-top: 1px solid rgba(0,0,0,0.06);\n}\n\n.footer-socials{\n  li a{\n    width: 45px;\n    height:45px;\n    background: $base-color;\n    color: $light;\n    display: inline-block;\n    text-align: center;\n    border-radius:100%;\n    padding-top: 12px;\n  }\n}\n\n\n.widget-contact{\n  h6{\n    font-weight: 500;\n    margin-bottom: 18px;\n\n    i{\n      color: $secondary-color;\n    }\n  }\n\n\n}\n\n.subscribe{\n  position: relative;\n  .form-control{\n    border-radius:50px;\n    height:60px;\n    padding-left: 25px;\n    border-color:#eee;\n  }\n  .btn{\n    position: absolute;\n    right:6px;\n    top:6px;\n  }\n}\n\n//  Back To Top\n//  \n\n.backtop{\n  position: fixed;\n  background: $secondary-color;\n  z-index: 9999;\n  display: inline-block;\n  right: 55px;\n  width: 60px;\n  height:60px;\n  bottom: 50px;\n  text-align: center;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  opacity: 0;\n  border-radius: 50px;\n\n  i{\n    color: $light;\n    font-size: 20px;\n  }\n}\n\n\n\n.reveal{\n    transition: all .3s;\n    cursor: pointer;\n    opacity: 1;\n}\n"]}