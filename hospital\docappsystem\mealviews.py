from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q
from django.core.paginator import Paginator
from dasapp.models import (
    CustomUser, AddPatient, MealPlan, Meal, MealDelivery, MealStatusLog,
    MEAL_STATUS_CHOICES, MEAL_TYPE_CHOICES, DIETARY_RESTRICTION_CHOICES
)
import qrcode
import io
import uuid
import base64
from datetime import datetime, timedelta

# Helper function to generate QR code
def generate_qr_code(data):
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")
    
    # Convert image to base64 string
    buffer = io.BytesIO()
    img.save(buffer, format="PNG")
    img_str = base64.b64encode(buffer.getvalue()).decode()
    
    return f"data:image/png;base64,{img_str}"

# Dashboard view for meal tracking
@login_required(login_url='/')
def meal_dashboard(request):
    # Get counts for different meal statuses
    preparing_count = MealDelivery.objects.filter(status='preparing').count()
    ready_count = MealDelivery.objects.filter(status='ready').count()
    in_transit_count = MealDelivery.objects.filter(status='in_transit').count()
    delivered_count = MealDelivery.objects.filter(status='delivered').count()
    consumed_count = MealDelivery.objects.filter(status='consumed').count()
    returned_count = MealDelivery.objects.filter(status='returned').count()
    disposed_count = MealDelivery.objects.filter(status='disposed').count()
    
    # Get recent meal deliveries
    recent_deliveries = MealDelivery.objects.all().order_by('-created_at')[:10]
    
    # Get today's meal deliveries
    today = timezone.now().date()
    today_deliveries = MealDelivery.objects.filter(
        scheduled_time__date=today
    ).order_by('scheduled_time')
    
    context = {
        'preparing_count': preparing_count,
        'ready_count': ready_count,
        'in_transit_count': in_transit_count,
        'delivered_count': delivered_count,
        'consumed_count': consumed_count,
        'returned_count': returned_count,
        'disposed_count': disposed_count,
        'recent_deliveries': recent_deliveries,
        'today_deliveries': today_deliveries,
    }
    
    return render(request, 'meals/dashboard.html', context)

# Meal tracking view
@login_required(login_url='/')
def meal_tracking(request):
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    date_filter = request.GET.get('date', '')
    patient_filter = request.GET.get('patient', '')
    
    # Start with all meal deliveries
    meal_deliveries = MealDelivery.objects.all()
    
    # Apply filters
    if status_filter:
        meal_deliveries = meal_deliveries.filter(status=status_filter)
    
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            meal_deliveries = meal_deliveries.filter(scheduled_time__date=filter_date)
        except ValueError:
            pass  # Invalid date format, ignore filter
    
    if patient_filter:
        meal_deliveries = meal_deliveries.filter(
            Q(patient__name__icontains=patient_filter)
        )
    
    # Order by scheduled time
    meal_deliveries = meal_deliveries.order_by('-scheduled_time')
    
    # Pagination
    paginator = Paginator(meal_deliveries, 20)  # Show 20 deliveries per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'status_choices': MEAL_STATUS_CHOICES,
        'status_filter': status_filter,
        'date_filter': date_filter,
        'patient_filter': patient_filter,
    }
    
    return render(request, 'meals/meal_tracking.html', context)

# Kitchen view for meal preparation
@login_required(login_url='/')
def kitchen_dashboard(request):
    # Get today's and upcoming meal deliveries
    today = timezone.now().date()
    today_deliveries = MealDelivery.objects.filter(
        scheduled_time__date=today,
        status__in=['preparing', 'ready']
    ).order_by('scheduled_time')
    
    upcoming_deliveries = MealDelivery.objects.filter(
        scheduled_time__date__gt=today,
        status='preparing'
    ).order_by('scheduled_time')[:10]
    
    # Get counts for different meal statuses
    preparing_count = MealDelivery.objects.filter(status='preparing').count()
    ready_count = MealDelivery.objects.filter(status='ready').count()
    returned_count = MealDelivery.objects.filter(status='returned').count()
    
    context = {
        'today_deliveries': today_deliveries,
        'upcoming_deliveries': upcoming_deliveries,
        'preparing_count': preparing_count,
        'ready_count': ready_count,
        'returned_count': returned_count,
    }
    
    return render(request, 'meals/kitchen_dashboard.html', context)

# View for creating a new meal
@login_required(login_url='/')
def create_meal(request):
    if request.method == 'POST':
        # Get form data
        meal_plan_id = request.POST.get('meal_plan')
        meal_type = request.POST.get('meal_type')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        calories = request.POST.get('calories')
        protein = request.POST.get('protein')
        carbs = request.POST.get('carbs')
        fat = request.POST.get('fat')
        preparation_instructions = request.POST.get('preparation_instructions', '')
        
        # Validate required fields
        if not all([meal_plan_id, meal_type, name, calories, protein, carbs, fat]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('create_meal')
        
        try:
            # Get the meal plan
            meal_plan = MealPlan.objects.get(id=meal_plan_id)
            
            # Create the meal
            meal = Meal.objects.create(
                meal_plan=meal_plan,
                meal_type=meal_type,
                name=name,
                description=description,
                calories=int(calories),
                protein=int(protein),
                carbs=int(carbs),
                fat=int(fat),
                preparation_instructions=preparation_instructions
            )
            
            messages.success(request, f'Meal "{name}" created successfully')
            return redirect('meal_detail', meal_id=meal.id)
        
        except Exception as e:
            messages.error(request, f'Error creating meal: {str(e)}')
            return redirect('create_meal')
    
    # GET request - show the form
    meal_plans = MealPlan.objects.filter(is_active=True)
    
    context = {
        'meal_plans': meal_plans,
        'meal_types': MEAL_TYPE_CHOICES,
    }
    
    return render(request, 'meals/create_meal.html', context)

# View for meal detail
@login_required(login_url='/')
def meal_detail(request, meal_id):
    meal = get_object_or_404(Meal, id=meal_id)
    
    # Get recent deliveries of this meal
    recent_deliveries = MealDelivery.objects.filter(meal=meal).order_by('-scheduled_time')[:10]
    
    context = {
        'meal': meal,
        'recent_deliveries': recent_deliveries,
    }
    
    return render(request, 'meals/meal_detail.html', context)

# View for creating a meal delivery
@login_required(login_url='/')
def create_meal_delivery(request):
    if request.method == 'POST':
        # Get form data
        meal_id = request.POST.get('meal')
        patient_id = request.POST.get('patient')
        scheduled_time_str = request.POST.get('scheduled_time')
        notes = request.POST.get('notes', '')
        
        # Validate required fields
        if not all([meal_id, patient_id, scheduled_time_str]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('create_meal_delivery')
        
        try:
            # Get the meal and patient
            meal = Meal.objects.get(id=meal_id)
            patient = AddPatient.objects.get(id=patient_id)
            
            # Parse scheduled time
            scheduled_time = datetime.strptime(scheduled_time_str, '%Y-%m-%dT%H:%M')
            
            # Generate a unique QR code
            qr_code_data = str(uuid.uuid4())
            
            # Create the meal delivery
            meal_delivery = MealDelivery.objects.create(
                meal=meal,
                patient=patient,
                prepared_by=request.user,
                qr_code=qr_code_data,
                status='preparing',
                scheduled_time=scheduled_time,
                notes=notes
            )
            
            # Create initial status log
            MealStatusLog.objects.create(
                meal_delivery=meal_delivery,
                previous_status='',
                new_status='preparing',
                changed_by=request.user,
                notes='Initial status'
            )
            
            messages.success(request, f'Meal delivery created successfully')
            return redirect('meal_delivery_detail', delivery_id=meal_delivery.id)
        
        except Exception as e:
            messages.error(request, f'Error creating meal delivery: {str(e)}')
            return redirect('create_meal_delivery')
    
    # GET request - show the form
    meals = Meal.objects.all()
    patients = AddPatient.objects.all()
    
    context = {
        'meals': meals,
        'patients': patients,
    }
    
    return render(request, 'meals/create_meal_delivery.html', context)

# View for meal delivery detail
@login_required(login_url='/')
def meal_delivery_detail(request, delivery_id):
    meal_delivery = get_object_or_404(MealDelivery, id=delivery_id)
    
    # Generate QR code
    qr_code_image = generate_qr_code(meal_delivery.qr_code)
    
    # Get status logs
    status_logs = MealStatusLog.objects.filter(meal_delivery=meal_delivery).order_by('-created_at')
    
    context = {
        'delivery': meal_delivery,
        'qr_code_image': qr_code_image,
        'status_logs': status_logs,
        'status_choices': MEAL_STATUS_CHOICES,
    }
    
    return render(request, 'meals/meal_delivery_detail.html', context)

# View for updating meal delivery status
@login_required(login_url='/')
def update_meal_status(request, delivery_id):
    if request.method == 'POST':
        meal_delivery = get_object_or_404(MealDelivery, id=delivery_id)
        new_status = request.POST.get('status')
        notes = request.POST.get('notes', '')
        
        # Validate the new status
        valid_statuses = [status[0] for status in MEAL_STATUS_CHOICES]
        if new_status not in valid_statuses:
            messages.error(request, 'Invalid status')
            return redirect('meal_delivery_detail', delivery_id=delivery_id)
        
        # Create status log
        previous_status = meal_delivery.status
        MealStatusLog.objects.create(
            meal_delivery=meal_delivery,
            previous_status=previous_status,
            new_status=new_status,
            changed_by=request.user,
            notes=notes
        )
        
        # Update the meal delivery status
        meal_delivery.update_status(new_status, request.user)
        
        messages.success(request, f'Status updated to {dict(MEAL_STATUS_CHOICES)[new_status]}')
        return redirect('meal_delivery_detail', delivery_id=delivery_id)
    
    # GET request - redirect to detail page
    return redirect('meal_delivery_detail', delivery_id=delivery_id)

# View for scanning QR code
@login_required(login_url='/')
def scan_qr_code(request):
    if request.method == 'POST':
        qr_code = request.POST.get('qr_code')
        
        try:
            # Find the meal delivery with this QR code
            meal_delivery = MealDelivery.objects.get(qr_code=qr_code)
            return redirect('meal_delivery_detail', delivery_id=meal_delivery.id)
        
        except MealDelivery.DoesNotExist:
            messages.error(request, 'Invalid QR code')
            return redirect('scan_qr_code')
    
    # GET request - show the scanning page
    return render(request, 'meals/scan_qr_code.html')

# View for printing QR code
@login_required(login_url='/')
def print_qr_code(request, delivery_id):
    meal_delivery = get_object_or_404(MealDelivery, id=delivery_id)
    
    # Generate QR code
    qr_code_image = generate_qr_code(meal_delivery.qr_code)
    
    context = {
        'delivery': meal_delivery,
        'qr_code_image': qr_code_image,
    }
    
    return render(request, 'meals/print_qr_code.html', context)

# View for meal plans
@login_required(login_url='/')
def meal_plans(request):
    meal_plans = MealPlan.objects.all().order_by('-created_at')
    
    context = {
        'meal_plans': meal_plans,
    }
    
    return render(request, 'meals/meal_plans.html', context)

# View for creating a meal plan
@login_required(login_url='/')
def create_meal_plan(request):
    if request.method == 'POST':
        # Get form data
        patient_id = request.POST.get('patient')
        dietary_restrictions = request.POST.get('dietary_restrictions')
        special_instructions = request.POST.get('special_instructions', '')
        calories_per_day = request.POST.get('calories_per_day')
        protein_per_day = request.POST.get('protein_per_day')
        carbs_per_day = request.POST.get('carbs_per_day')
        fat_per_day = request.POST.get('fat_per_day')
        
        # Validate required fields
        if not all([patient_id, dietary_restrictions, calories_per_day, protein_per_day, carbs_per_day, fat_per_day]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('create_meal_plan')
        
        try:
            # Get the patient
            patient = AddPatient.objects.get(id=patient_id)
            
            # Create the meal plan
            meal_plan = MealPlan.objects.create(
                patient=patient,
                created_by=request.user,
                updated_by=request.user,
                dietary_restrictions=dietary_restrictions,
                special_instructions=special_instructions,
                calories_per_day=int(calories_per_day),
                protein_per_day=int(protein_per_day),
                carbs_per_day=int(carbs_per_day),
                fat_per_day=int(fat_per_day),
                is_active=True
            )
            
            messages.success(request, f'Meal plan created successfully for {patient.name}')
            return redirect('meal_plan_detail', plan_id=meal_plan.id)
        
        except Exception as e:
            messages.error(request, f'Error creating meal plan: {str(e)}')
            return redirect('create_meal_plan')
    
    # GET request - show the form
    patients = AddPatient.objects.all()
    
    context = {
        'patients': patients,
        'dietary_restrictions': DIETARY_RESTRICTION_CHOICES,
    }
    
    return render(request, 'meals/create_meal_plan.html', context)

# View for meal plan detail
@login_required(login_url='/')
def meal_plan_detail(request, plan_id):
    meal_plan = get_object_or_404(MealPlan, id=plan_id)
    
    # Get meals for this plan
    meals = Meal.objects.filter(meal_plan=meal_plan).order_by('meal_type')
    
    context = {
        'meal_plan': meal_plan,
        'meals': meals,
    }
    
    return render(request, 'meals/meal_plan_detail.html', context)
