from django.db import models
from django.utils import timezone
from .models import CustomUser, AddPatient

# Meal status options
MEAL_STATUS_CHOICES = [
    ('preparing', 'Preparing in Kitchen'),
    ('ready', 'Ready for Delivery'),
    ('in_transit', 'In Transit'),
    ('delivered', 'Delivered to Patient'),
    ('consumed', 'Consumed'),
    ('returned', 'Returned to Kitchen'),
    ('disposed', 'Disposed'),
]

# Meal types
MEAL_TYPE_CHOICES = [
    ('breakfast', 'Breakfast'),
    ('lunch', 'Lunch'),
    ('dinner', 'Dinner'),
    ('snack', 'Snack'),
]

# Dietary restrictions
DIETARY_RESTRICTION_CHOICES = [
    ('none', 'No Restrictions'),
    ('vegetarian', 'Vegetarian'),
    ('vegan', 'Vegan'),
    ('gluten_free', 'Gluten Free'),
    ('dairy_free', 'Dairy Free'),
    ('nut_free', 'Nut Free'),
    ('low_sodium', 'Low Sodium'),
    ('diabetic', 'Diabetic'),
    ('low_fat', 'Low Fat'),
    ('kosher', 'Kosher'),
    ('halal', 'Halal'),
]

class MealPlan(models.Model):
    """Model for patient meal plans"""
    patient = models.ForeignKey(AddPatient, on_delete=models.CASCADE, related_name='meal_plans')
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='created_meal_plans')
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='updated_meal_plans')
    dietary_restrictions = models.CharField(max_length=50, choices=DIETARY_RESTRICTION_CHOICES, default='none')
    special_instructions = models.TextField(blank=True, null=True)
    calories_per_day = models.IntegerField(default=2000)
    protein_per_day = models.IntegerField(default=50)  # in grams
    carbs_per_day = models.IntegerField(default=250)   # in grams
    fat_per_day = models.IntegerField(default=70)      # in grams
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Meal Plan for {self.patient.name}"

class Meal(models.Model):
    """Model for individual meals"""
    meal_plan = models.ForeignKey(MealPlan, on_delete=models.CASCADE, related_name='meals')
    meal_type = models.CharField(max_length=20, choices=MEAL_TYPE_CHOICES)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    calories = models.IntegerField()
    protein = models.IntegerField()  # in grams
    carbs = models.IntegerField()    # in grams
    fat = models.IntegerField()      # in grams
    preparation_instructions = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.meal_type})"

class MealDelivery(models.Model):
    """Model for tracking meal deliveries"""
    meal = models.ForeignKey(Meal, on_delete=models.CASCADE, related_name='deliveries')
    patient = models.ForeignKey(AddPatient, on_delete=models.CASCADE, related_name='meal_deliveries')
    prepared_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='prepared_meals')
    delivered_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='delivered_meals')
    collected_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='collected_meals')
    qr_code = models.CharField(max_length=255, unique=True)
    status = models.CharField(max_length=20, choices=MEAL_STATUS_CHOICES, default='preparing')
    scheduled_time = models.DateTimeField()
    preparation_time = models.DateTimeField(null=True, blank=True)
    delivery_time = models.DateTimeField(null=True, blank=True)
    consumption_time = models.DateTimeField(null=True, blank=True)
    return_time = models.DateTimeField(null=True, blank=True)
    feedback = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.meal.name} for {self.patient.name} - {self.get_status_display()}"
    
    def update_status(self, new_status, user=None):
        """Update the status and corresponding timestamp"""
        self.status = new_status
        
        # Update the corresponding timestamp based on the new status
        now = timezone.now()
        if new_status == 'ready':
            self.preparation_time = now
        elif new_status == 'delivered':
            self.delivery_time = now
            self.delivered_by = user
        elif new_status == 'consumed':
            self.consumption_time = now
        elif new_status == 'returned' or new_status == 'disposed':
            self.return_time = now
            self.collected_by = user
            
        self.save()

class MealStatusLog(models.Model):
    """Model for logging meal status changes"""
    meal_delivery = models.ForeignKey(MealDelivery, on_delete=models.CASCADE, related_name='status_logs')
    previous_status = models.CharField(max_length=20, choices=MEAL_STATUS_CHOICES)
    new_status = models.CharField(max_length=20, choices=MEAL_STATUS_CHOICES)
    changed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.meal_delivery} - {self.previous_status} to {self.new_status}"
