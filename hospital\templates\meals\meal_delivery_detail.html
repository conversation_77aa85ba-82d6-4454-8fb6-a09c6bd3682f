{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Meal Delivery Details</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'meal_dashboard' %}">Meal Tracking</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'meal_tracking' %}">All Meals</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Delivery Details</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Meal Delivery Info -->
    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meal Delivery Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Meal Name</label>
                                <div class="form-control-static">{{ delivery.meal.name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Meal Type</label>
                                <div class="form-control-static">{{ delivery.meal.get_meal_type_display }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Patient Name</label>
                                <div class="form-control-static">{{ delivery.patient.name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Patient Room</label>
                                <div class="form-control-static">{{ delivery.patient.room_no }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Scheduled Time</label>
                                <div class="form-control-static">{{ delivery.scheduled_time|date:"M d, Y h:i A" }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Current Status</label>
                                <div class="form-control-static">
                                    <span class="badge 
                                        {% if delivery.status == 'preparing' %}badge-warning
                                        {% elif delivery.status == 'ready' %}badge-info
                                        {% elif delivery.status == 'in_transit' %}badge-primary
                                        {% elif delivery.status == 'delivered' %}badge-success
                                        {% elif delivery.status == 'consumed' %}badge-secondary
                                        {% elif delivery.status == 'returned' %}badge-danger
                                        {% elif delivery.status == 'disposed' %}badge-dark
                                        {% endif %}">
                                        {{ delivery.get_status_display }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Notes</label>
                                <div class="form-control-static">{{ delivery.notes|default:"No notes provided" }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Meal Description</label>
                                <div class="form-control-static">{{ delivery.meal.description|default:"No description provided" }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Calories</label>
                                <div class="form-control-static">{{ delivery.meal.calories }} kcal</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Protein</label>
                                <div class="form-control-static">{{ delivery.meal.protein }} g</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Carbs</label>
                                <div class="form-control-static">{{ delivery.meal.carbs }} g</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">QR Code</h5>
                </div>
                <div class="card-body text-center">
                    <img src="{{ qr_code_image }}" alt="QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                    <div>
                        <a href="{% url 'print_qr_code' delivery.id %}" class="btn btn-primary" target="_blank">
                            <i class="fas fa-print mr-2"></i> Print QR Code
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">Update Status</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'update_meal_status' delivery.id %}">
                        {% csrf_token %}
                        <div class="form-group">
                            <label>New Status</label>
                            <select name="status" class="form-control">
                                {% for status_code, status_name in status_choices %}
                                <option value="{{ status_code }}" {% if delivery.status == status_code %}selected{% endif %}>{{ status_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="form-group">
                            <label>Notes</label>
                            <textarea name="notes" class="form-control" rows="3" placeholder="Add notes about this status change"></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-save mr-2"></i> Update Status
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Timeline -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Status Timeline</h5>
                </div>
                <div class="card-body">
                    <div class="meal-timeline">
                        <div class="timeline-container">
                            <div class="timeline-steps">
                                <div class="timeline-step {% if delivery.status == 'preparing' or delivery.status == 'ready' or delivery.status == 'in_transit' or delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                    <div class="timeline-icon">
                                        <i class="fas fa-utensils"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Preparing</h6>
                                        <p>{{ delivery.created_at|date:"M d, Y h:i A"|default:"Not started" }}</p>
                                    </div>
                                </div>
                                <div class="timeline-step {% if delivery.status == 'ready' or delivery.status == 'in_transit' or delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                    <div class="timeline-icon">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Ready</h6>
                                        <p>{{ delivery.preparation_time|date:"M d, Y h:i A"|default:"Not ready" }}</p>
                                    </div>
                                </div>
                                <div class="timeline-step {% if delivery.status == 'in_transit' or delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                    <div class="timeline-icon">
                                        <i class="fas fa-truck"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>In Transit</h6>
                                        <p>{% if delivery.status == 'in_transit' or delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}In progress{% else %}Not started{% endif %}</p>
                                    </div>
                                </div>
                                <div class="timeline-step {% if delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                    <div class="timeline-icon">
                                        <i class="fas fa-check-double"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Delivered</h6>
                                        <p>{{ delivery.delivery_time|date:"M d, Y h:i A"|default:"Not delivered" }}</p>
                                    </div>
                                </div>
                                <div class="timeline-step {% if delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                    <div class="timeline-icon">
                                        <i class="fas fa-utensil-spoon"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Consumed</h6>
                                        <p>{{ delivery.consumption_time|date:"M d, Y h:i A"|default:"Not consumed" }}</p>
                                    </div>
                                </div>
                                <div class="timeline-step {% if delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                    <div class="timeline-icon">
                                        <i class="fas fa-undo-alt"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6>Returned/Disposed</h6>
                                        <p>{{ delivery.return_time|date:"M d, Y h:i A"|default:"Not returned" }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status History -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Status History</h5>
                </div>
                <div class="card-body">
                    {% if status_logs %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Date & Time</th>
                                    <th>Previous Status</th>
                                    <th>New Status</th>
                                    <th>Changed By</th>
                                    <th>Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in status_logs %}
                                <tr>
                                    <td>{{ log.created_at|date:"M d, Y h:i A" }}</td>
                                    <td>
                                        {% if log.previous_status %}
                                        <span class="badge 
                                            {% if log.previous_status == 'preparing' %}badge-warning
                                            {% elif log.previous_status == 'ready' %}badge-info
                                            {% elif log.previous_status == 'in_transit' %}badge-primary
                                            {% elif log.previous_status == 'delivered' %}badge-success
                                            {% elif log.previous_status == 'consumed' %}badge-secondary
                                            {% elif log.previous_status == 'returned' %}badge-danger
                                            {% elif log.previous_status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ log.get_previous_status_display }}
                                        </span>
                                        {% else %}
                                        <span class="badge badge-secondary">Initial</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge 
                                            {% if log.new_status == 'preparing' %}badge-warning
                                            {% elif log.new_status == 'ready' %}badge-info
                                            {% elif log.new_status == 'in_transit' %}badge-primary
                                            {% elif log.new_status == 'delivered' %}badge-success
                                            {% elif log.new_status == 'consumed' %}badge-secondary
                                            {% elif log.new_status == 'returned' %}badge-danger
                                            {% elif log.new_status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ log.get_new_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ log.changed_by.username }}</td>
                                    <td>{{ log.notes|default:"" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No status history available.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .meal-timeline {
        width: 100%;
        padding: 20px 0;
    }
    
    .timeline-container {
        width: 100%;
        position: relative;
    }
    
    .timeline-steps {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }
    
    .timeline-step {
        position: relative;
        width: 16.66%;
        text-align: center;
    }
    
    .timeline-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #e9ecef;
        margin: 0 auto;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 20px;
        color: #6c757d;
        position: relative;
        z-index: 2;
    }
    
    .timeline-step.active .timeline-icon {
        background-color: #28a745;
        color: white;
    }
    
    .timeline-content {
        padding-top: 10px;
    }
    
    .timeline-content h6 {
        margin-bottom: 5px;
        font-weight: 600;
    }
    
    .timeline-content p {
        font-size: 12px;
        color: #6c757d;
        margin: 0;
    }
    
    .timeline-step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 25px;
        left: 50%;
        width: 100%;
        height: 2px;
        background-color: #e9ecef;
        z-index: 1;
    }
    
    .timeline-step.active:not(:last-child)::after {
        background-color: #28a745;
    }
    
    .form-control-static {
        padding-top: 7px;
        padding-bottom: 7px;
        margin-bottom: 0;
        min-height: 34px;
    }
</style>
{% endblock %}
