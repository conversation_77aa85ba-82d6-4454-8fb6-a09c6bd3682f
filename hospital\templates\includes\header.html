{% load static %}
{% if user.user_type == '1' %}
<div class="header">
    <div class="header-left">
       <a href="{% url 'admin_home' %}" class="logo">
       <img src="{% static 'assets/img/logo.png'%}" alt="Logo">
       <strong>HMS</strong>
       </a>
    </div>

    <div class="top-nav-search">
       <h4>Hospital Management System</h4>
    </div>
    <a class="mobile_btn" id="mobile_btn">
    <i class="fas fa-bars"></i>
    </a>
    <ul class="nav user-menu">
       <li class="nav-item dropdown has-arrow">
          <a href="#" class="dropdown-toggle nav-link" data-toggle="dropdown">
             <span class="user-img">
                <img class="rounded-circle" src="/{{user.profile_pic}}" alt="{{user.first_name}}">
                <span class="status online"></span>
             </span>
             <span class="d-none d-lg-inline-block">{{user.first_name}}</span>
          </a>
          <div class="dropdown-menu">
             <div class="user-header">
                <div class="avatar avatar-sm">
                   <img src="/{{user.profile_pic}}" alt="User Image" class="avatar-img rounded-circle">
                </div>
                <div class="user-text">
                  <h6>{{user.first_name}} {{user.last_name}}</h6>
                   <p class="text-muted mb-0">Administrator</p>
                </div>
             </div>
             <a class="dropdown-item" href="{% url 'profile' %}">
                <i class="fas fa-user mr-2"></i> My Profile
             </a>
             <a class="dropdown-item" href="{% url 'change_password' %}">
                <i class="fas fa-cog mr-2"></i> Settings
             </a>
             <a class="dropdown-item" href="{% url 'logout' %}">
                <i class="fas fa-sign-out-alt mr-2"></i> Logout
             </a>
          </div>
       </li>
    </ul>
 </div>

 {% elif user.user_type == '2'  %}
 <div class="header">
   <div class="header-left">
      <a href="{% url 'doctor_home' %}" class="logo">
      <img src="{% static 'assets/img/logo.png'%}" alt="Logo">
      <strong>HMS</strong>
      </a>
   </div>

   <div class="top-nav-search">
      <h4>Hospital Management System</h4>
   </div>
   <a class="mobile_btn" id="mobile_btn">
   <i class="fas fa-bars"></i>
   </a>
   <ul class="nav user-menu">
      <li class="nav-item dropdown has-arrow">
         <a href="#" class="dropdown-toggle nav-link" data-toggle="dropdown">
            <span class="user-img">
               <img class="rounded-circle" src="/{{user.profile_pic}}" alt="{{user.first_name}}">
               <span class="status online"></span>
            </span>
            <span class="d-none d-lg-inline-block">Dr. {{user.first_name}}</span>
         </a>
         <div class="dropdown-menu">
            <div class="user-header">
               <div class="avatar avatar-sm">
                  <img src="/{{user.profile_pic}}" alt="User Image" class="avatar-img rounded-circle">
               </div>
               <div class="user-text">
                  <h6>Dr. {{user.first_name}} {{user.last_name}}</h6>
                  <p class="text-muted mb-0">Doctor</p>
               </div>
            </div>
            <a class="dropdown-item" href="{% url 'profile' %}">
               <i class="fas fa-user-md mr-2"></i> My Profile
            </a>
            <a class="dropdown-item" href="{% url 'change_password' %}">
               <i class="fas fa-cog mr-2"></i> Settings
            </a>
            <a class="dropdown-item" href="{% url 'logout' %}">
               <i class="fas fa-sign-out-alt mr-2"></i> Logout
            </a>
         </div>
      </li>
   </ul>
</div>
{% else %}
<div class="header">
   <div class="header-left">
      <a href="{% url 'userhome' %}" class="logo">
      <img src="{% static 'assets/img/logo.png'%}" alt="Logo">
      <strong>HMS</strong>
      </a>
   </div>

   <div class="top-nav-search">
      <h4>Hospital Management System</h4>
   </div>
   <a class="mobile_btn" id="mobile_btn">
   <i class="fas fa-bars"></i>
   </a>
   <ul class="nav user-menu">
      <li class="nav-item dropdown has-arrow">
         <a href="#" class="dropdown-toggle nav-link" data-toggle="dropdown">
            <span class="user-img">
               <img class="rounded-circle" src="/{{user.profile_pic}}" alt="{{user.first_name}}">
               <span class="status online"></span>
            </span>
            <span class="d-none d-lg-inline-block">{{user.first_name}}</span>
         </a>
         <div class="dropdown-menu">
            <div class="user-header">
               <div class="avatar avatar-sm">
                  <img src="/{{user.profile_pic}}" alt="User Image" class="avatar-img rounded-circle">
               </div>
               <div class="user-text">
                  <h6>{{user.first_name}} {{user.last_name}}</h6>
                  <p class="text-muted mb-0">Patient</p>
               </div>
            </div>
            <a class="dropdown-item" href="{% url 'profile' %}">
               <i class="fas fa-user mr-2"></i> My Profile
            </a>
            <a class="dropdown-item" href="{% url 'change_password' %}">
               <i class="fas fa-cog mr-2"></i> Settings
            </a>
            <a class="dropdown-item" href="{% url 'logout' %}">
               <i class="fas fa-sign-out-alt mr-2"></i> Logout
            </a>
         </div>
      </li>
   </ul>
</div>

{% endif %}