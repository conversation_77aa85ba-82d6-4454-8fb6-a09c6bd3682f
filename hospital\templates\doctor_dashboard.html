{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Welcome Dr. {{ user.first_name }}!</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item active">Dashboard</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Overview Statistics -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-calendar-check text-primary"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ total_appointments }}</h3>
                            <h6>Total Appointments</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-users text-success"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ total_patients }}</h3>
                            <h6>My Patients</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-calendar-day text-warning"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ today_appointments }}</h3>
                            <h6>Today's Appointments</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensils text-info"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ meal_orders }}</h3>
                            <h6>Meal Orders</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Overview Statistics -->

    <div class="row">
        <!-- Today's Appointments -->
        <div class="col-md-6 d-flex">
            <div class="card card-table flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Today's Appointments</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Patient Name</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in appointments %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="#">{{ appointment.pat_id.admin.first_name }} {{ appointment.pat_id.admin.last_name }}</a>
                                        </h2>
                                    </td>
                                    <td>{{ appointment.time_of_appointment }}</td>
                                    <td>
                                        {% if appointment.status == 'Pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                        {% elif appointment.status == 'Approved' %}
                                        <span class="badge badge-success">Approved</span>
                                        {% elif appointment.status == 'Completed' %}
                                        <span class="badge badge-info">Completed</span>
                                        {% elif appointment.status == 'Cancelled' %}
                                        <span class="badge badge-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="#" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No appointments for today</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Today's Appointments -->

        <!-- Patient Meal Orders -->
        <div class="col-md-6 d-flex">
            <div class="card card-table flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Recent Meal Orders</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Patient</th>
                                    <th>Meal</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for meal in meals %}
                                <tr>
                                    <td>{{ meal.patient.name }}</td>
                                    <td>{{ meal.meal.name }}</td>
                                    <td>
                                        {% if meal.status == 'preparing' %}
                                        <span class="badge badge-warning">Preparing</span>
                                        {% elif meal.status == 'ready' %}
                                        <span class="badge badge-info">Ready</span>
                                        {% elif meal.status == 'in_transit' %}
                                        <span class="badge badge-primary">In Transit</span>
                                        {% elif meal.status == 'delivered' %}
                                        <span class="badge badge-success">Delivered</span>
                                        {% elif meal.status == 'consumed' %}
                                        <span class="badge badge-secondary">Consumed</span>
                                        {% elif meal.status == 'returned' %}
                                        <span class="badge badge-danger">Returned</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'doctor_meal_delivery_detail' meal.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No meal orders found</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Patient Meal Orders -->
    </div>

    <div class="row">
        <!-- Appointment Statistics -->
        <div class="col-md-6 d-flex">
            <div class="card flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Appointment Statistics</h5>
                </div>
                <div class="card-body">
                    <div id="appointment-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <!-- /Appointment Statistics -->

        <!-- Patient Distribution -->
        <div class="col-md-6 d-flex">
            <div class="card flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Patient Age Distribution</h5>
                </div>
                <div class="card-body">
                    <div id="patient-age-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <!-- /Patient Distribution -->
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'doctor_appointment' %}">
                                        <i class="fas fa-calendar-plus fa-3x mb-3 text-primary"></i>
                                        <h5>Appointments</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'doctor_patient' %}">
                                        <i class="fas fa-user-injured fa-3x mb-3 text-success"></i>
                                        <h5>My Patients</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'doctor_order_meal' %}">
                                        <i class="fas fa-utensils fa-3x mb-3 text-warning"></i>
                                        <h5>Order Meal</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'profile' %}">
                                        <i class="fas fa-user-md fa-3x mb-3 text-info"></i>
                                        <h5>My Profile</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Quick Links -->
</div>
{% endblock %}

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Appointment Statistics Chart
        var appointmentOptions = {
            series: [{
                name: 'Appointments',
                data: [31, 40, 28, 51, 42, 82, 56]
            }],
            chart: {
                height: 300,
                type: 'area',
                toolbar: {
                    show: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth'
            },
            xaxis: {
                type: 'datetime',
                categories: [
                    "2023-05-01", "2023-05-02", "2023-05-03", "2023-05-04", 
                    "2023-05-05", "2023-05-06", "2023-05-07"
                ]
            },
            colors: ['#4361ee'],
            tooltip: {
                x: {
                    format: 'dd/MM/yy'
                },
            },
        };

        var appointmentChart = new ApexCharts(document.querySelector("#appointment-chart"), appointmentOptions);
        appointmentChart.render();

        // Patient Age Distribution Chart
        var patientAgeOptions = {
            series: [25, 15, 44, 55, 41, 17],
            chart: {
                width: '100%',
                height: 300,
                type: 'pie',
            },
            labels: ['0-10', '11-20', '21-30', '31-40', '41-50', '51+'],
            colors: ['#4361ee', '#3f37c9', '#4cc9f0', '#4895ef', '#f72585', '#e63946'],
            legend: {
                position: 'bottom'
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        var patientAgeChart = new ApexCharts(document.querySelector("#patient-age-chart"), patientAgeOptions);
        patientAgeChart.render();
    });
</script>
{% endblock %}
