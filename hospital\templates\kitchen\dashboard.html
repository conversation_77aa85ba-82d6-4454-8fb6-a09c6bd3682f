{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <!-- Page Header -->
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Kitchen Management Dashboard</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Home</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Dashboard</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Status Cards -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensils text-warning"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ preparing_count }}</h3>
                            <h6>Preparing</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-check-circle text-info"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ ready_count }}</h3>
                            <h6>Ready for Delivery</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-truck text-primary"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ in_transit_count|default:"0" }}</h3>
                            <h6>In Transit</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-check-double text-success"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ delivered_count }}</h3>
                            <h6>Delivered</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'kitchen_orders' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-clipboard-list mr-2"></i> View Orders
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'kitchen_meals' %}" class="btn btn-info btn-block">
                                <i class="fas fa-utensils mr-2"></i> Manage Meals
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'add_meal' %}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle mr-2"></i> Add New Meal
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'scan_qr_code' %}" class="btn btn-warning btn-block">
                                <i class="fas fa-qrcode mr-2"></i> Scan QR Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Today's Orders -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Today's Orders</h5>
                </div>
                <div class="card-body">
                    {% if today_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Room</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in today_deliveries %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="{% url 'kitchen_meal_detail' delivery.meal.id %}">{{ delivery.meal.name }}</a>
                                        </h2>
                                        <small>{{ delivery.meal.get_meal_type_display }}</small>
                                    </td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.patient.room_no }}</td>
                                    <td>{{ delivery.scheduled_time|date:"h:i A" }}</td>
                                    <td>
                                        <span class="badge
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% elif delivery.status == 'in_transit' %}badge-primary
                                            {% elif delivery.status == 'delivered' %}badge-success
                                            {% elif delivery.status == 'consumed' %}badge-secondary
                                            {% elif delivery.status == 'returned' %}badge-danger
                                            {% elif delivery.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'print_qr_code' delivery.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-qrcode"></i>
                                            </a>
                                            {% if delivery.status == 'preparing' %}
                                            <form method="post" action="{% url 'update_meal_status' delivery.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <input type="hidden" name="status" value="ready">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meals scheduled for today.</div>
                    {% endif %}
                </div>
            </div>
        </div>
        <!-- /Today's Orders -->

        <!-- Meal Type Distribution -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Today's Meal Types</h5>
                </div>
                <div class="card-body">
                    {% if meal_types %}
                    <div class="chart-container">
                        <canvas id="mealTypeChart"></canvas>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meal data available for today.</div>
                    {% endif %}
                </div>
            </div>
        </div>
        <!-- /Meal Type Distribution -->
    </div>

    <div class="row">
        <!-- Upcoming Meals -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Upcoming Meals</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_meals %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in upcoming_meals|slice:":5" %}
                                <tr>
                                    <td>{{ delivery.meal.name }}</td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"M d, Y h:i A" }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                        {% if upcoming_meals.count > 5 %}
                        <div class="text-center mt-3">
                            <a href="{% url 'kitchen_orders' %}" class="btn btn-sm btn-primary">View All</a>
                        </div>
                        {% endif %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">No upcoming meals scheduled.</div>
                    {% endif %}
                </div>
            </div>
        </div>
        <!-- /Upcoming Meals -->

        <!-- Meal Preparation Timeline -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meal Preparation Timeline</h5>
                </div>
                <div class="card-body">
                    <div id="preparation-timeline" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <!-- /Meal Preparation Timeline -->
    </div>
</div>

<style>
    .actions {
        display: flex;
        gap: 5px;
    }

    .chart-container {
        position: relative;
        height: 250px;
    }
</style>

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        {% if meal_types %}
        // Meal type chart
        var ctx = document.getElementById('mealTypeChart').getContext('2d');
        var mealTypeChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: [
                    {% for meal_type in meal_types %}
                    '{{ meal_type.meal__meal_type|title }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for meal_type in meal_types %}
                        {{ meal_type.count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        '#4361ee',
                        '#3f37c9',
                        '#4cc9f0',
                        '#4895ef'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
        {% endif %}

        // Preparation Timeline Chart
        var timelineOptions = {
            series: [{
                data: [
                    {
                        x: 'Breakfast',
                        y: [new Date('2023-05-18T06:00:00').getTime(), new Date('2023-05-18T07:30:00').getTime()]
                    },
                    {
                        x: 'Lunch',
                        y: [new Date('2023-05-18T10:00:00').getTime(), new Date('2023-05-18T12:30:00').getTime()]
                    },
                    {
                        x: 'Dinner',
                        y: [new Date('2023-05-18T16:00:00').getTime(), new Date('2023-05-18T18:30:00').getTime()]
                    },
                    {
                        x: 'Snacks',
                        y: [new Date('2023-05-18T14:00:00').getTime(), new Date('2023-05-18T15:00:00').getTime()]
                    }
                ]
            }],
            chart: {
                height: 300,
                type: 'rangeBar',
                toolbar: {
                    show: false
                }
            },
            plotOptions: {
                bar: {
                    horizontal: true,
                    barHeight: '50%',
                    rangeBarGroupRows: true
                }
            },
            colors: ['#4361ee', '#3f37c9', '#4cc9f0', '#4895ef'],
            xaxis: {
                type: 'datetime'
            },
            tooltip: {
                x: {
                    format: 'HH:mm'
                }
            }
        };

        var timelineChart = new ApexCharts(document.querySelector("#preparation-timeline"), timelineOptions);
        timelineChart.render();
    });
</script>
{% endblock %}
{% endblock %}
