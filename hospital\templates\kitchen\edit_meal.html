{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Edit Meal</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_meals' %}">Manage Meals</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_meal_detail' meal.id %}">{{ meal.name }}</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Edit</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Edit Meal Information</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'edit_meal' meal.id %}">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Meal Plan</label>
                                    <div class="form-control-static">{{ meal.meal_plan.patient.name }} - {{ meal.meal_plan.get_dietary_restrictions_display }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Meal Type <span class="text-danger">*</span></label>
                                    <select name="meal_type" class="form-control" required>
                                        {% for type_code, type_name in meal_type_choices %}
                                        <option value="{{ type_code }}" {% if meal.meal_type == type_code %}selected{% endif %}>{{ type_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Meal Name <span class="text-danger">*</span></label>
                                    <input type="text" name="name" class="form-control" value="{{ meal.name }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea name="description" class="form-control" rows="3">{{ meal.description }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Calories (kcal) <span class="text-danger">*</span></label>
                                    <input type="number" name="calories" class="form-control" value="{{ meal.calories }}" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Protein (g) <span class="text-danger">*</span></label>
                                    <input type="number" name="protein" class="form-control" value="{{ meal.protein }}" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Carbs (g) <span class="text-danger">*</span></label>
                                    <input type="number" name="carbs" class="form-control" value="{{ meal.carbs }}" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Fat (g) <span class="text-danger">*</span></label>
                                    <input type="number" name="fat" class="form-control" value="{{ meal.fat }}" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Preparation Instructions</label>
                                    <textarea name="preparation_instructions" class="form-control" rows="5">{{ meal.preparation_instructions }}</textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 text-right">
                                <a href="{% url 'kitchen_meal_detail' meal.id %}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-2"></i> Save Changes
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control-static {
        padding-top: 7px;
        padding-bottom: 7px;
        margin-bottom: 0;
        min-height: 34px;
    }
</style>
{% endblock %}
