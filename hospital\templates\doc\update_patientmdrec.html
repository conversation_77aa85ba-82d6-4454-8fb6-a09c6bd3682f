{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">

    <div class="page-header">
    <div class="row align-items-center">
    <div class="col">
    <h3 class="page-title">Manage Patient Medical Record</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">View Patient Medical Record</li>
    </ul>
    </div>
    
    </div>
    </div>
    
    <div class="row">
    <div class="col-sm-12">
    <div class="card card-table">
    <div class="card-body">
        {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'error' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
           {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'success' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
    <div class="table-responsive">
    <table class="table table-hover table-center mb-0 datatable">
      
        <tr align="center">
            <th colspan="8" >Patient Details</th> 
           </tr>
        <tr>
            
            <th>Patient Name</th>
            <td>{{pd.name}}</td>
            <th>Patient Contact Number</th>
            <td>{{pd.mobilenumber}}</td>

          </tr>
          <tr>
            
            <th>Patient Address</th>
            <td>{{pd.address}}</td>
            <th>Patient Gender</th>
            <td>{{pd.gender}}</td>
          </tr>
          <tr>
            
            <th>Patient Email</th>
            <td>{{pd.email}}</td>
            <th>Patient Age</th>
            <td>{{pd.age}}</td>
          </tr>
          <tr>
            
         
            <th>Medical History</th>
            <td>{{pd.medicalhistory}}</td>
          </tr>
          <tr>
              
    </table>

    <table id="datatable" class="table table-bordered dt-responsive nowrap" style="border-collapse: collapse; border-spacing: 0; width: 100%;">
        <tr align="center">
         <th colspan="8" >Medical History</th> 
        </tr>
        <tr>
          <th>#</th>
      <th>Blood Pressure</th>
      <th>Weight</th>
      <th>Blood Sugar</th>
      <th>Body Temprature</th>
      <th>Medical Prescription</th>
      <th>Visit Date</th>
      </tr>
      {% for i in mrd %}
      <tr>
        <td>{{forloop.counter}}</td>
       <td>{{i.bloodpressure}}</td>
       <td>{{i.weight}}</td>
       <td>{{i.bloodsugar}}</td> 
        <td>{{i.bodytemp}}</td>
        <td>{{i.prescription}}</td>
        <td>{{i.visitingdate_at}}</td> 
      </tr>
      {% endfor %}
      </table>
    
    
              </div>
              <p align="center"  style="padding-top: 20px">                            
                <button class="btn btn-primary waves-effect waves-light w-lg" data-toggle="modal" data-target="#myModal">Add Medical History</button></p> 
              <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                   <div class="modal-content">
                    <div class="modal-header">
                                                                  <h5 class="modal-title" id="exampleModalLabel">Take Action</h5>
                                                                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                      <span aria-hidden="true">&times;</span>
                                                                  </button>
                                                              </div>
                                                              <div class="modal-body">
                                                              <table class="table table-bordered table-hover data-tables">
              
                                               <form method="POST" action="{% url 'updatemedrecpatient' %}">
                                                {% csrf_token %}
              
                                              
                                             
                   <tr>
                  <th>Blood Pressure :</th>
                  <td>
                    <input class="form-control wd-450" required="true" name="bloodpressure" type="text">
                  </td>
                  <input type="hidden" value="{{pd.id}}" name="p_id" id="p_id" class="form-control" required="">
                </tr> 
                <tr>
                    <th>Weight :</th>
                    <td>
                      <input type="text" class="form-control wd-450" required="true" name="weight">
                    </td>
                   
                  </tr> 
                  <tr>
                    <th>Blood Sugar :</th>
                    <td>
                      <input type="text" class="form-control wd-450" required="true" name="bloodsugar">
                    </td>
                   
                  </tr> 
                  <tr>
                    <th>Body Tempperature :</th>
                    <td>
                      <input type="text" class="form-control wd-450" required="true" name="bodytemp">
                    </td>
                   
                  </tr> 
                  <tr>
                    <th>Prescription :</th>
                    <td>
                      <textarea class="form-control wd-450" required="true" name="prescription"></textarea>
                    </td>
                   
                  </tr> 
              </table>
              </div>
              <div class="modal-footer">
               <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
               <button type="submit" name="submit" class="btn btn-primary">Update</button>
                
                </form>
                
              
              </div>
              
                                    
                                      </div>
                                  </div>
              
                          </div>
              
                        </div>
    

    </div>
    </div>
    </div>
    </div>
    </div>
    </div>


{% endblock %}