{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Kitchen Inventory</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Inventory</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 col-sm-6 mb-3">
                            <button class="btn btn-success btn-block" data-toggle="modal" data-target="#addItemModal">
                                <i class="fas fa-plus-circle mr-2"></i> Add New Item
                            </button>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <button class="btn btn-info btn-block" data-toggle="modal" data-target="#stockTakeModal">
                                <i class="fas fa-clipboard-check mr-2"></i> Stock Take
                            </button>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <button class="btn btn-warning btn-block" data-toggle="modal" data-target="#orderSuppliesModal">
                                <i class="fas fa-shopping-cart mr-2"></i> Order Supplies
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Status -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="db-info">
                            <h3>24</h3>
                            <h6>In Stock</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="db-info">
                            <h3>8</h3>
                            <h6>Low Stock</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="db-info">
                            <h3>3</h3>
                            <h6>Out of Stock</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="db-info">
                            <h3>5</h3>
                            <h6>Pending Orders</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory List -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="row align-items-center">
                        <div class="col">
                            <h5 class="card-title">Inventory Items</h5>
                        </div>
                        <div class="col-auto">
                            <div class="form-group mb-0">
                                <input type="text" class="form-control" placeholder="Search items...">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0 datatable">
                            <thead>
                                <tr>
                                    <th>Item Name</th>
                                    <th>Category</th>
                                    <th>Quantity</th>
                                    <th>Unit</th>
                                    <th>Status</th>
                                    <th>Last Updated</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Rice</td>
                                    <td>Grains</td>
                                    <td>25</td>
                                    <td>kg</td>
                                    <td><span class="badge badge-success">In Stock</span></td>
                                    <td>May 15, 2023</td>
                                    <td>
                                        <div class="actions">
                                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editItemModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" data-toggle="modal" data-target="#adjustStockModal">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Chicken Breast</td>
                                    <td>Meat</td>
                                    <td>8</td>
                                    <td>kg</td>
                                    <td><span class="badge badge-warning">Low Stock</span></td>
                                    <td>May 16, 2023</td>
                                    <td>
                                        <div class="actions">
                                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editItemModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" data-toggle="modal" data-target="#adjustStockModal">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Broccoli</td>
                                    <td>Vegetables</td>
                                    <td>0</td>
                                    <td>kg</td>
                                    <td><span class="badge badge-danger">Out of Stock</span></td>
                                    <td>May 14, 2023</td>
                                    <td>
                                        <div class="actions">
                                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editItemModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" data-toggle="modal" data-target="#adjustStockModal">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Olive Oil</td>
                                    <td>Oils</td>
                                    <td>12</td>
                                    <td>liters</td>
                                    <td><span class="badge badge-success">In Stock</span></td>
                                    <td>May 12, 2023</td>
                                    <td>
                                        <div class="actions">
                                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editItemModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" data-toggle="modal" data-target="#adjustStockModal">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Milk</td>
                                    <td>Dairy</td>
                                    <td>5</td>
                                    <td>liters</td>
                                    <td><span class="badge badge-warning">Low Stock</span></td>
                                    <td>May 17, 2023</td>
                                    <td>
                                        <div class="actions">
                                            <button class="btn btn-sm btn-info" data-toggle="modal" data-target="#editItemModal">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-sm btn-warning" data-toggle="modal" data-target="#adjustStockModal">
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Categories -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Inventory Categories</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-bread-slice fa-3x mb-3 text-primary"></i>
                                    <h5 class="card-title">Grains & Bread</h5>
                                    <p class="card-text">8 items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-drumstick-bite fa-3x mb-3 text-danger"></i>
                                    <h5 class="card-title">Meat & Poultry</h5>
                                    <p class="card-text">6 items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-carrot fa-3x mb-3 text-success"></i>
                                    <h5 class="card-title">Vegetables</h5>
                                    <p class="card-text">12 items</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <i class="fas fa-apple-alt fa-3x mb-3 text-warning"></i>
                                    <h5 class="card-title">Fruits</h5>
                                    <p class="card-text">9 items</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Item Modal -->
<div class="modal fade" id="addItemModal" tabindex="-1" role="dialog" aria-labelledby="addItemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addItemModalLabel">Add New Inventory Item</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Item Name <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Category <span class="text-danger">*</span></label>
                                <select class="form-control" required>
                                    <option value="">Select Category</option>
                                    <option>Grains</option>
                                    <option>Meat</option>
                                    <option>Vegetables</option>
                                    <option>Fruits</option>
                                    <option>Dairy</option>
                                    <option>Oils</option>
                                    <option>Spices</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Quantity <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" required>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Unit <span class="text-danger">*</span></label>
                                <select class="form-control" required>
                                    <option value="">Select Unit</option>
                                    <option>kg</option>
                                    <option>g</option>
                                    <option>liters</option>
                                    <option>ml</option>
                                    <option>pcs</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Minimum Stock Level</label>
                                <input type="number" class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Description</label>
                                <textarea class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary">Save Item</button>
            </div>
        </div>
    </div>
</div>

<style>
    .actions {
        display: flex;
        gap: 5px;
    }
</style>
{% endblock %}
