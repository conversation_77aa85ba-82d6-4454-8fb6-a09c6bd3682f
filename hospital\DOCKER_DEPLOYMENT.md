# Hospital Management System - Docker Deployment Guide

This guide provides comprehensive instructions for deploying the Hospital Management System using Docker with nginx web server support.

## 🚀 Quick Start

### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 2GB RAM
- 10GB free disk space

### Development Deployment
```bash
# Clone and navigate to the project
cd hospital

# Copy environment file
cp .env.development .env

# Start development environment
docker-compose -f docker-compose.dev.yml up -d

# Access the application
open http://localhost
```

### Production Deployment
```bash
# Copy and configure environment
cp .env.production .env
# Edit .env with your production settings

# Start production environment
docker-compose up -d

# Access the application
open http://your-domain.com
```

## 📋 Table of Contents
- [Architecture Overview](#architecture-overview)
- [Environment Configuration](#environment-configuration)
- [Deployment Options](#deployment-options)
- [Nginx Configuration](#nginx-configuration)
- [Database Setup](#database-setup)
- [SSL/HTTPS Setup](#ssl-https-setup)
- [Monitoring and Logging](#monitoring-and-logging)
- [Backup and Restore](#backup-and-restore)
- [Troubleshooting](#troubleshooting)

## 🏗️ Architecture Overview

The Docker setup includes:

- **nginx**: Reverse proxy, static file serving, SSL termination
- **Django Web App**: Hospital management application
- **PostgreSQL/MySQL**: Primary database
- **Redis**: Caching and session storage
- **Backup Service**: Automated database backups

### Container Architecture
```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   nginx     │────│  Django     │────│ PostgreSQL  │
│   (Port 80) │    │   (Port     │    │ (Port 5432) │
│             │    │    8000)    │    │             │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                   ┌─────────────┐
                   │    Redis    │
                   │ (Port 6379) │
                   └─────────────┘
```

## ⚙️ Environment Configuration

### 1. Copy Environment Template
```bash
cp .env.template .env
```

### 2. Configure Key Settings

#### Database Configuration
```env
# PostgreSQL (Recommended for production)
DATABASE_ENGINE=postgresql
DB_NAME=hospital_db
DB_USER=postgres
DB_PASSWORD=your_secure_password

# MySQL Alternative
DATABASE_ENGINE=mysql
DB_NAME=hospital_db
DB_USER=hospital_user
DB_PASSWORD=your_secure_password
```

#### Security Settings
```env
SECRET_KEY=your-very-long-random-secret-key
DEBUG=False
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
```

#### Admin User
```env
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=secure_admin_password
```

## 🚢 Deployment Options

### Option 1: PostgreSQL (Recommended)
```bash
docker-compose up -d
```

### Option 2: MySQL
```bash
docker-compose -f docker-compose.mysql.yml up -d
```

### Option 3: Development
```bash
docker-compose -f docker-compose.dev.yml up -d
```

### Option 4: SQLite (Single Container)
```bash
# Set DATABASE_ENGINE=sqlite3 in .env
docker build -t hospital-app .
docker run -p 80:8000 -v $(pwd)/db.sqlite3:/app/db.sqlite3 hospital-app
```

## 🌐 Nginx Configuration

### Features Included
- **Reverse Proxy**: Routes requests to Django backend
- **Static File Serving**: Efficient serving of CSS, JS, images
- **Gzip Compression**: Reduces bandwidth usage
- **Rate Limiting**: Protects against abuse
- **Security Headers**: XSS protection, CSRF protection
- **Caching**: Static file and API response caching
- **SSL/TLS Support**: HTTPS configuration ready

### Custom Configuration
Edit `nginx/nginx.conf` to customize:
- Rate limiting rules
- Cache settings
- Security headers
- SSL configuration

### Rate Limiting Zones
- **Login**: 5 requests per minute
- **API**: 10 requests per second
- **General**: 1 request per second

## 🗄️ Database Setup

### PostgreSQL Setup
```bash
# Access PostgreSQL container
docker-compose exec db psql -U postgres -d hospital_db

# Create additional users
CREATE USER hospital_readonly WITH PASSWORD 'readonly_password';
GRANT SELECT ON ALL TABLES IN SCHEMA public TO hospital_readonly;
```

### MySQL Setup
```bash
# Access MySQL container
docker-compose exec db mysql -u root -p

# Create additional users
CREATE USER 'hospital_readonly'@'%' IDENTIFIED BY 'readonly_password';
GRANT SELECT ON hospital_db.* TO 'hospital_readonly'@'%';
FLUSH PRIVILEGES;
```

### Initial Data Loading
```bash
# For SQLite with initial data
LOAD_INITIAL_DATA=true docker-compose up -d
```

## 🔒 SSL/HTTPS Setup

### 1. Obtain SSL Certificates
```bash
# Using Let's Encrypt (recommended)
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com
```

### 2. Configure SSL in nginx
```bash
# Create SSL directory
mkdir -p ssl

# Copy certificates
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/
```

### 3. Update nginx Configuration
Uncomment the HTTPS server block in `nginx/nginx.conf` and update:
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/ssl/fullchain.pem;
    ssl_certificate_key /etc/ssl/privkey.pem;
    
    # ... rest of configuration
}
```

### 4. Enable HTTPS Redirect
Update `.env`:
```env
SECURE_SSL_REDIRECT=True
SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True
```

## 📊 Monitoring and Logging

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f nginx
docker-compose logs -f web
docker-compose logs -f db
```

### Log Files Location
- **nginx**: `/var/log/nginx/` (in nginx container)
- **Django**: `/app/logs/django.log` (in web container)
- **PostgreSQL**: Docker logs

### Health Checks
```bash
# Check service health
docker-compose ps

# Manual health check
curl http://localhost/health
```

## 💾 Backup and Restore

### Automated Backups
```bash
# Run backup manually
docker-compose exec backup /scripts/backup.sh

# Schedule with cron (on host)
0 2 * * * cd /path/to/hospital && docker-compose exec backup /scripts/backup.sh
```

### Manual Backup
```bash
# PostgreSQL
docker-compose exec db pg_dump -U postgres hospital_db > backup.sql

# MySQL
docker-compose exec db mysqldump -u root -p hospital_db > backup.sql
```

### Restore Database
```bash
# Using restore script
docker-compose exec backup /scripts/restore.sh /backups/hospital_db_backup_20231201_120000.sql.gz

# Manual restore (PostgreSQL)
docker-compose exec db psql -U postgres -d hospital_db < backup.sql
```

## 🔧 Maintenance Commands

### Update Application
```bash
# Pull latest changes
git pull

# Rebuild and restart
docker-compose build --no-cache
docker-compose up -d

# Run migrations
docker-compose exec web python manage.py migrate
```

### Scale Services
```bash
# Scale web service
docker-compose up -d --scale web=3

# Update nginx upstream accordingly
```

### Clean Up
```bash
# Remove unused images
docker image prune -a

# Remove unused volumes
docker volume prune

# Complete cleanup
docker system prune -a --volumes
```

## 🐛 Troubleshooting

### Common Issues

#### 1. Permission Denied Errors
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x docker-entrypoint.sh scripts/*.sh
```

#### 2. Database Connection Issues
```bash
# Check database status
docker-compose exec db pg_isready -U postgres

# Reset database
docker-compose down -v
docker-compose up -d
```

#### 3. Static Files Not Loading
```bash
# Collect static files
docker-compose exec web python manage.py collectstatic --noinput

# Check nginx configuration
docker-compose exec nginx nginx -t
```

#### 4. Memory Issues
```bash
# Check resource usage
docker stats

# Increase memory limits in docker-compose.yml
```

### Debug Mode
```bash
# Enable debug mode
echo "DEBUG=True" >> .env
docker-compose restart web
```

### Container Shell Access
```bash
# Access web container
docker-compose exec web bash

# Access database container
docker-compose exec db bash

# Access nginx container
docker-compose exec nginx sh
```

## 📈 Performance Optimization

### 1. Enable Redis Caching
```env
REDIS_URL=redis://:password@redis:6379/1
```

### 2. Optimize nginx
- Enable gzip compression (already configured)
- Set appropriate cache headers
- Use nginx for static file serving

### 3. Database Optimization
```sql
-- PostgreSQL optimization
ANALYZE;
REINDEX DATABASE hospital_db;
```

### 4. Monitor Performance
```bash
# Check response times
curl -w "@curl-format.txt" -o /dev/null -s http://localhost/

# Monitor resource usage
docker stats --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}"
```

## 🔐 Security Best Practices

1. **Change Default Passwords**: Update all default passwords in `.env`
2. **Use Strong Secrets**: Generate secure random keys
3. **Enable HTTPS**: Always use SSL in production
4. **Regular Updates**: Keep Docker images updated
5. **Firewall Rules**: Restrict access to necessary ports only
6. **Backup Encryption**: Encrypt sensitive backups
7. **Monitor Logs**: Set up log monitoring and alerts

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review Docker and nginx logs
3. Consult the Django documentation
4. Create an issue in the project repository

---

**Note**: This deployment guide assumes familiarity with Docker, nginx, and basic system administration. Always test in a staging environment before deploying to production.
