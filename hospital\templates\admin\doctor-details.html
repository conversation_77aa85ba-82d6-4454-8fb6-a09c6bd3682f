{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">

    <div class="page-header">
    <div class="row align-items-center">
    <div class="col">
    <h3 class="page-title">Doctor Details</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Doctor Details</li>
    </ul>
    </div>
   
    </div>
    </div>
    
    <div class="row">
    <div class="col-sm-12">
    <div class="card card-table">
    <div class="card-body">
        {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'error' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
           {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'success' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
    <div class="table-responsive">
        <table class="table table-hover table-center mb-0 datatable">
            {% for i in doctorlist1 %}
            
              <tr>
                  
                  <th>Name of Doctor</th>
                  <td>{{i.admin.first_name}} {{i.admin.last_name}}</td>
                  <th>Contact Number</th>
                  <td>{{i.mobilenumber}}</td>
                </tr>
                <tr>
                  
                  <th>Email Address</th>
                  <td>{{i.admin.email}}</td>
                  <th>Doctor Specialization</th>
                  <td>{{i.specialization_id.sname}}</td>
                </tr>
                <tr>
                  
                  <th>Profile Pic</th>
                  
                  <td><img src="{{i.admin.profile_pic.url}}" width="50" height="50"></td>
                  <th>Date of Registrations</th>
                  <td>{{i.regdate_at}}</td>
                </tr>{% endfor %}
          </table>
          <div class="btn btn-primary"><a href="{% url 'viewdoctorlist' %}">Back</a></div>
    </div>
    </div>
    </div>
    </div>
    </div>
    </div>


{% endblock %}