{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Meal Tracking</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'meal_dashboard' %}">Meal Tracking</a></li>
                        <li class="breadcrumb-item active" aria-current="page">All Meals</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Filter Meals</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'meal_tracking' %}">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Status</label>
                                    <select name="status" class="form-control">
                                        <option value="">All Statuses</option>
                                        {% for status_code, status_name in status_choices %}
                                        <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>{{ status_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Date</label>
                                    <input type="date" name="date" class="form-control" value="{{ date_filter }}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label>Patient Name</label>
                                    <input type="text" name="patient" class="form-control" placeholder="Search by patient name" value="{{ patient_filter }}">
                                </div>
                            </div>
                        </div>
                        <div class="text-right">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter mr-2"></i> Apply Filters
                            </button>
                            <a href="{% url 'meal_tracking' %}" class="btn btn-secondary">
                                <i class="fas fa-redo mr-2"></i> Reset
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Meal Tracking Timeline -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meal Tracking Timeline</h5>
                    <div class="card-tools">
                        <a href="{% url 'create_meal_delivery' %}" class="btn btn-primary">
                            <i class="fas fa-plus-circle mr-2"></i> New Meal Delivery
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Timeline</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in page_obj %}
                                <tr>
                                    <td>{{ delivery.meal.name }}</td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"M d, Y h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% elif delivery.status == 'in_transit' %}badge-primary
                                            {% elif delivery.status == 'delivered' %}badge-success
                                            {% elif delivery.status == 'consumed' %}badge-secondary
                                            {% elif delivery.status == 'returned' %}badge-danger
                                            {% elif delivery.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="meal-timeline">
                                            <div class="timeline-steps">
                                                <div class="timeline-step {% if delivery.status == 'preparing' or delivery.status == 'ready' or delivery.status == 'in_transit' or delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                                    <div class="timeline-content">
                                                        <i class="fas fa-utensils"></i>
                                                    </div>
                                                </div>
                                                <div class="timeline-step {% if delivery.status == 'ready' or delivery.status == 'in_transit' or delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                                    <div class="timeline-content">
                                                        <i class="fas fa-check-circle"></i>
                                                    </div>
                                                </div>
                                                <div class="timeline-step {% if delivery.status == 'in_transit' or delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                                    <div class="timeline-content">
                                                        <i class="fas fa-truck"></i>
                                                    </div>
                                                </div>
                                                <div class="timeline-step {% if delivery.status == 'delivered' or delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                                    <div class="timeline-content">
                                                        <i class="fas fa-check-double"></i>
                                                    </div>
                                                </div>
                                                <div class="timeline-step {% if delivery.status == 'consumed' or delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                                    <div class="timeline-content">
                                                        <i class="fas fa-utensil-spoon"></i>
                                                    </div>
                                                </div>
                                                <div class="timeline-step {% if delivery.status == 'returned' or delivery.status == 'disposed' %}active{% endif %}">
                                                    <div class="timeline-content">
                                                        <i class="fas fa-undo-alt"></i>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'print_qr_code' delivery.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-qrcode"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <div class="pagination justify-content-center mt-4">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if patient_filter %}&patient={{ patient_filter }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if patient_filter %}&patient={{ patient_filter }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if patient_filter %}&patient={{ patient_filter }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if patient_filter %}&patient={{ patient_filter }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if patient_filter %}&patient={{ patient_filter }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="alert alert-info">No meal deliveries found matching your criteria.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .meal-timeline {
        width: 100%;
    }
    
    .timeline-steps {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .timeline-step {
        position: relative;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #e9ecef;
        display: flex;
        justify-content: center;
        align-items: center;
    }
    
    .timeline-step.active {
        background-color: #28a745;
        color: white;
    }
    
    .timeline-step:not(:last-child)::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 100%;
        width: calc(100% - 20px);
        height: 2px;
        background-color: #e9ecef;
        transform: translateY(-50%);
        z-index: -1;
    }
    
    .timeline-step.active:not(:last-child)::after {
        background-color: #28a745;
    }
    
    .timeline-content {
        font-size: 10px;
    }
    
    .actions {
        display: flex;
        gap: 5px;
    }
</style>
{% endblock %}
