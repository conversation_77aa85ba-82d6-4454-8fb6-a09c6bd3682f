# Git
.git
.gitignore
README.md
DOCKER_DEPLOYMENT.md

# Docker
Dockerfile*
docker-compose*.yml
.dockerignore

# Environment files
.env*
!.env.template

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Database
*.sqlite3
*.db

# Media files (uncomment if you don't want to include uploaded files)
# media/

# Backup files
backups/
*.sql
*.dump

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Cache
.cache/
*.cache

# Temporary files
tmp/
temp/
*.tmp

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Documentation builds
docs/_build/
