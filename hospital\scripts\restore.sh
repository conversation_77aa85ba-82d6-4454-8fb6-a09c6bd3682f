#!/bin/bash
# Database restore script for Hospital Management System

set -e

# Configuration
BACKUP_DIR="/backups"
DB_NAME=${DB_NAME:-hospital_db}
DB_USER=${DB_USER:-postgres}
DB_HOST=${DB_HOST:-db}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if backup file is provided
if [ -z "$1" ]; then
    print_error "Usage: $0 <backup_file>"
    print_status "Available backups:"
    ls -la "$BACKUP_DIR"/hospital_db_backup_*.gz 2>/dev/null || echo "No backups found"
    exit 1
fi

BACKUP_FILE="$1"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    print_error "Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Extract if compressed
if [[ "$BACKUP_FILE" == *.gz ]]; then
    print_status "Extracting compressed backup..."
    EXTRACTED_FILE="${BACKUP_FILE%.gz}"
    gunzip -c "$BACKUP_FILE" > "$EXTRACTED_FILE"
    BACKUP_FILE="$EXTRACTED_FILE"
fi

print_warning "This will replace the current database. Are you sure? (y/N)"
read -r response
if [[ ! "$response" =~ ^[Yy]$ ]]; then
    print_status "Restore cancelled"
    exit 0
fi

# PostgreSQL restore
if [ "$DATABASE_ENGINE" = "postgresql" ]; then
    print_status "Restoring PostgreSQL database from: $BACKUP_FILE"
    
    # Drop and recreate database
    psql -h "$DB_HOST" -U "$DB_USER" -c "DROP DATABASE IF EXISTS $DB_NAME;"
    psql -h "$DB_HOST" -U "$DB_USER" -c "CREATE DATABASE $DB_NAME;"
    
    # Restore from backup
    psql -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" < "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        print_status "PostgreSQL restore completed successfully"
    else
        print_error "PostgreSQL restore failed"
        exit 1
    fi

# MySQL restore
elif [ "$DATABASE_ENGINE" = "mysql" ]; then
    print_status "Restoring MySQL database from: $BACKUP_FILE"
    
    # Drop and recreate database
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -e "DROP DATABASE IF EXISTS $DB_NAME;"
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE $DB_NAME;"
    
    # Restore from backup
    mysql -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        print_status "MySQL restore completed successfully"
    else
        print_error "MySQL restore failed"
        exit 1
    fi

# SQLite restore
else
    print_status "Restoring SQLite database from: $BACKUP_FILE"
    
    cp "$BACKUP_FILE" /app/db.sqlite3
    
    if [ $? -eq 0 ]; then
        print_status "SQLite restore completed successfully"
    else
        print_error "SQLite restore failed"
        exit 1
    fi
fi

# Clean up extracted file if it was compressed
if [[ "$1" == *.gz ]] && [ -f "$EXTRACTED_FILE" ]; then
    rm "$EXTRACTED_FILE"
fi

print_status "Database restore completed successfully"
