
from django.contrib import admin
from .models import *
from django.contrib.auth.admin import UserAdmin



# Register your models here.
class UserModel(UserAdmin):
    list_display =['username','email','user_type']
admin.site.register(CustomUser,UserModel)
admin.site.register(Specialization)
admin.site.register(DoctorReg)
admin.site.register(Appointment)
admin.site.register(Page)

# Register meal tracking models
class MealPlanAdmin(admin.ModelAdmin):
    list_display = ['patient', 'dietary_restrictions', 'is_active', 'created_at']
    list_filter = ['dietary_restrictions', 'is_active']
    search_fields = ['patient__name', 'special_instructions']

class MealAdmin(admin.ModelAdmin):
    list_display = ['name', 'meal_type', 'calories', 'protein', 'carbs', 'fat']
    list_filter = ['meal_type']
    search_fields = ['name', 'description']

class MealDeliveryAdmin(admin.ModelAdmin):
    list_display = ['meal', 'patient', 'status', 'scheduled_time', 'qr_code']
    list_filter = ['status', 'scheduled_time']
    search_fields = ['meal__name', 'patient__name', 'qr_code']
    readonly_fields = ['qr_code', 'preparation_time', 'delivery_time', 'consumption_time', 'return_time']

class MealStatusLogAdmin(admin.ModelAdmin):
    list_display = ['meal_delivery', 'previous_status', 'new_status', 'changed_by', 'created_at']
    list_filter = ['previous_status', 'new_status', 'created_at']
    search_fields = ['meal_delivery__meal__name', 'meal_delivery__patient__name', 'notes']
    readonly_fields = ['meal_delivery', 'previous_status', 'new_status', 'changed_by', 'created_at']

admin.site.register(MealPlan, MealPlanAdmin)
admin.site.register(Meal, MealAdmin)
admin.site.register(MealDelivery, MealDeliveryAdmin)
admin.site.register(MealStatusLog, MealStatusLogAdmin)
