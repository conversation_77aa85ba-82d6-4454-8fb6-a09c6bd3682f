{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Welcome {{ user.first_name }}!</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item active">Dashboard</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
    <!-- /Page Header -->

    <!-- Overview Statistics -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-calendar-check text-primary"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ appointment_count }}</h3>
                            <h6>My Appointments</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-file-medical text-success"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ medical_record_count }}</h3>
                            <h6>Medical Records</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensils text-warning"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ meal_count }}</h3>
                            <h6>Today's Meals</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-user-md text-info"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ doctor_name }}</h3>
                            <h6>My Doctor</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Overview Statistics -->

    <div class="row">
        <!-- Upcoming Appointments -->
        <div class="col-md-6 d-flex">
            <div class="card card-table flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Upcoming Appointments</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Doctor</th>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for appointment in appointments %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="#">Dr. {{ appointment.doctor_id.admin.first_name }} {{ appointment.doctor_id.admin.last_name }}</a>
                                        </h2>
                                    </td>
                                    <td>{{ appointment.date_of_appointment }}</td>
                                    <td>{{ appointment.time_of_appointment }}</td>
                                    <td>
                                        {% if appointment.status == 'Pending' %}
                                        <span class="badge badge-warning">Pending</span>
                                        {% elif appointment.status == 'Approved' %}
                                        <span class="badge badge-success">Approved</span>
                                        {% elif appointment.status == 'Completed' %}
                                        <span class="badge badge-info">Completed</span>
                                        {% elif appointment.status == 'Cancelled' %}
                                        <span class="badge badge-danger">Cancelled</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No upcoming appointments</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Upcoming Appointments -->

        <!-- Today's Meals -->
        <div class="col-md-6 d-flex">
            <div class="card card-table flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Today's Meals</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for meal in meals %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="#">{{ meal.meal.name }}</a>
                                        </h2>
                                    </td>
                                    <td>{{ meal.scheduled_time|time:"h:i A" }}</td>
                                    <td>
                                        {% if meal.status == 'preparing' %}
                                        <span class="badge badge-warning">Preparing</span>
                                        {% elif meal.status == 'ready' %}
                                        <span class="badge badge-info">Ready</span>
                                        {% elif meal.status == 'in_transit' %}
                                        <span class="badge badge-primary">In Transit</span>
                                        {% elif meal.status == 'delivered' %}
                                        <span class="badge badge-success">Delivered</span>
                                        {% elif meal.status == 'consumed' %}
                                        <span class="badge badge-secondary">Consumed</span>
                                        {% elif meal.status == 'returned' %}
                                        <span class="badge badge-danger">Returned</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'patient_meal_delivery_detail' meal.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            {% if meal.status == 'delivered' %}
                                            <a href="{% url 'patient_meal_feedback' meal.id %}" class="btn btn-sm btn-success">
                                                <i class="fas fa-comment"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center">No meals scheduled for today</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <!-- /Today's Meals -->
    </div>

    <div class="row">
        <!-- Medical History -->
        <div class="col-md-6 d-flex">
            <div class="card flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Medical History</h5>
                </div>
                <div class="card-body">
                    <div id="medical-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <!-- /Medical History -->

        <!-- Meal Nutrition -->
        <div class="col-md-6 d-flex">
            <div class="card flex-fill">
                <div class="card-header">
                    <h5 class="card-title">Meal Nutrition</h5>
                </div>
                <div class="card-body">
                    <div id="nutrition-chart" style="height: 300px;"></div>
                </div>
            </div>
        </div>
        <!-- /Meal Nutrition -->
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'patientappointment' %}">
                                        <i class="fas fa-calendar-plus fa-3x mb-3 text-primary"></i>
                                        <h5>Book Appointment</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'patient_meal_dashboard' %}">
                                        <i class="fas fa-utensils fa-3x mb-3 text-success"></i>
                                        <h5>My Meals</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'profile' %}">
                                        <i class="fas fa-user fa-3x mb-3 text-warning"></i>
                                        <h5>My Profile</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <a href="{% url 'change_password' %}">
                                        <i class="fas fa-lock fa-3x mb-3 text-info"></i>
                                        <h5>Change Password</h5>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /Quick Links -->
</div>
{% endblock %}

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Medical History Chart
        var medicalOptions = {
            series: [{
                name: 'Blood Pressure',
                data: [120, 118, 130, 125, 122, 119, 121]
            }, {
                name: 'Weight (kg)',
                data: [75, 74, 74, 73, 73, 72, 72]
            }],
            chart: {
                height: 300,
                type: 'line',
                toolbar: {
                    show: false
                }
            },
            dataLabels: {
                enabled: false
            },
            stroke: {
                curve: 'smooth',
                width: 3
            },
            xaxis: {
                type: 'datetime',
                categories: [
                    "2023-04-01", "2023-04-15", "2023-05-01", "2023-05-15", 
                    "2023-06-01", "2023-06-15", "2023-07-01"
                ]
            },
            colors: ['#4361ee', '#f72585'],
            tooltip: {
                x: {
                    format: 'dd/MM/yy'
                },
            },
            legend: {
                position: 'top'
            }
        };

        var medicalChart = new ApexCharts(document.querySelector("#medical-chart"), medicalOptions);
        medicalChart.render();

        // Nutrition Chart
        var nutritionOptions = {
            series: [45, 30, 25],
            chart: {
                width: '100%',
                height: 300,
                type: 'pie',
            },
            labels: ['Carbs', 'Protein', 'Fat'],
            colors: ['#4361ee', '#4cc9f0', '#f72585'],
            legend: {
                position: 'bottom'
            },
            responsive: [{
                breakpoint: 480,
                options: {
                    chart: {
                        width: 200
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }]
        };

        var nutritionChart = new ApexCharts(document.querySelector("#nutrition-chart"), nutritionOptions);
        nutritionChart.render();
    });
</script>
{% endblock %}
