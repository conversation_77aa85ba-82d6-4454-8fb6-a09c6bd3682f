{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Kitchen Dashboard</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'meal_dashboard' %}">Meal Tracking</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Kitchen</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Status Cards -->
    <div class="row">
        <div class="col-xl-4 col-sm-6 col-12">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ preparing_count }}</h3>
                            <h6>Preparing</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-sm-6 col-12">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ ready_count }}</h3>
                            <h6>Ready for Delivery</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-4 col-sm-6 col-12">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-undo-alt"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ returned_count }}</h3>
                            <h6>Returned to Kitchen</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'meal_tracking' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-list-alt mr-2"></i> Meal Tracking
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'create_meal' %}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle mr-2"></i> New Meal
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'create_meal_delivery' %}" class="btn btn-info btn-block">
                                <i class="fas fa-plus-circle mr-2"></i> New Delivery
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'scan_qr_code' %}" class="btn btn-warning btn-block">
                                <i class="fas fa-qrcode mr-2"></i> Scan QR Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Meals -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Today's Meals</h5>
                </div>
                <div class="card-body">
                    {% if today_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in today_deliveries %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="{% url 'meal_detail' delivery.meal.id %}">{{ delivery.meal.name }}</a>
                                        </h2>
                                        <small>{{ delivery.meal.get_meal_type_display }}</small>
                                    </td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'print_qr_code' delivery.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-qrcode"></i>
                                            </a>
                                            {% if delivery.status == 'preparing' %}
                                            <form method="post" action="{% url 'update_meal_status' delivery.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <input type="hidden" name="status" value="ready">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check"></i> Mark Ready
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meals scheduled for today.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Meals -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Upcoming Meals</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in upcoming_deliveries %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="{% url 'meal_detail' delivery.meal.id %}">{{ delivery.meal.name }}</a>
                                        </h2>
                                        <small>{{ delivery.meal.get_meal_type_display }}</small>
                                    </td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"M d, Y h:i A" }}</td>
                                    <td>
                                        <span class="badge badge-warning">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No upcoming meals scheduled.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .actions {
        display: flex;
        gap: 5px;
    }
</style>
{% endblock %}
