{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Add New Meal</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_meals' %}">Manage Meals</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add New Meal</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meal Information</h5>
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'add_meal' %}">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Meal Plan <span class="text-danger">*</span></label>
                                    <select name="meal_plan" class="form-control" required>
                                        <option value="">Select Meal Plan</option>
                                        {% for plan in meal_plans %}
                                        <option value="{{ plan.id }}">{{ plan.patient.name }} - {{ plan.get_dietary_restrictions_display }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label>Meal Type <span class="text-danger">*</span></label>
                                    <select name="meal_type" class="form-control" required>
                                        <option value="">Select Meal Type</option>
                                        {% for type_code, type_name in meal_type_choices %}
                                        <option value="{{ type_code }}">{{ type_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Meal Name <span class="text-danger">*</span></label>
                                    <input type="text" name="name" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea name="description" class="form-control" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Calories (kcal) <span class="text-danger">*</span></label>
                                    <input type="number" name="calories" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Protein (g) <span class="text-danger">*</span></label>
                                    <input type="number" name="protein" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Carbs (g) <span class="text-danger">*</span></label>
                                    <input type="number" name="carbs" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-group">
                                    <label>Fat (g) <span class="text-danger">*</span></label>
                                    <input type="number" name="fat" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label>Preparation Instructions</label>
                                    <textarea name="preparation_instructions" class="form-control" rows="5"></textarea>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-12 text-right">
                                <a href="{% url 'kitchen_meals' %}" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-2"></i> Save Meal
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
