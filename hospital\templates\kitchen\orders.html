{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Meal Orders</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Meal Orders</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Filter Orders</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'kitchen_orders' %}">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label>Status</label>
                                <select name="status" class="form-control">
                                    <option value="">All Statuses</option>
                                    {% for status_code, status_name in status_choices %}
                                    <option value="{{ status_code }}" {% if status_filter == status_code %}selected{% endif %}>{{ status_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label>Date</label>
                                <input type="date" name="date" class="form-control" value="{{ date_filter }}">
                            </div>
                            <div class="col-md-3 mb-3">
                                <label>Meal Type</label>
                                <select name="meal_type" class="form-control">
                                    <option value="">All Types</option>
                                    {% for type_code, type_name in meal_type_choices %}
                                    <option value="{{ type_code }}" {% if meal_type_filter == type_code %}selected{% endif %}>{{ type_name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label>&nbsp;</label>
                                <button type="submit" class="btn btn-primary btn-block">
                                    <i class="fas fa-filter mr-2"></i> Apply Filters
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Orders List -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meal Orders</h5>
                </div>
                <div class="card-body">
                    {% if page_obj %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0 datatable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Room</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in page_obj %}
                                <tr>
                                    <td>{{ order.id }}</td>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="{% url 'kitchen_meal_detail' order.meal.id %}">{{ order.meal.name }}</a>
                                        </h2>
                                        <small>{{ order.meal.get_meal_type_display }}</small>
                                    </td>
                                    <td>{{ order.patient.name }}</td>
                                    <td>{{ order.patient.room_no }}</td>
                                    <td>{{ order.scheduled_time|date:"M d, Y h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if order.status == 'preparing' %}badge-warning
                                            {% elif order.status == 'ready' %}badge-info
                                            {% elif order.status == 'in_transit' %}badge-primary
                                            {% elif order.status == 'delivered' %}badge-success
                                            {% elif order.status == 'consumed' %}badge-secondary
                                            {% elif order.status == 'returned' %}badge-danger
                                            {% elif order.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ order.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'meal_delivery_detail' order.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'print_qr_code' order.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-qrcode"></i>
                                            </a>
                                            {% if order.status == 'preparing' %}
                                            <form method="post" action="{% url 'update_meal_status' order.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <input type="hidden" name="status" value="ready">
                                                <button type="submit" class="btn btn-sm btn-success">
                                                    <i class="fas fa-check"></i> Mark Ready
                                                </button>
                                            </form>
                                            {% elif order.status == 'ready' %}
                                            <form method="post" action="{% url 'update_meal_status' order.id %}" style="display: inline;">
                                                {% csrf_token %}
                                                <input type="hidden" name="status" value="in_transit">
                                                <button type="submit" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-truck"></i> Mark In Transit
                                                </button>
                                            </form>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- Pagination -->
                    {% if page_obj.has_other_pages %}
                    <div class="pagination justify-content-center mt-4">
                        <ul class="pagination">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if meal_type_filter %}&meal_type={{ meal_type_filter }}{% endif %}">Previous</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Previous</span>
                            </li>
                            {% endif %}
                            
                            {% for i in page_obj.paginator.page_range %}
                                {% if page_obj.number == i %}
                                <li class="page-item active">
                                    <span class="page-link">{{ i }}</span>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ i }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if meal_type_filter %}&meal_type={{ meal_type_filter }}{% endif %}">{{ i }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if status_filter %}&status={{ status_filter }}{% endif %}{% if date_filter %}&date={{ date_filter }}{% endif %}{% if meal_type_filter %}&meal_type={{ meal_type_filter }}{% endif %}">Next</a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">Next</span>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    {% else %}
                    <div class="alert alert-info">No meal orders found matching your criteria.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .actions {
        display: flex;
        gap: 5px;
    }
</style>
{% endblock %}
