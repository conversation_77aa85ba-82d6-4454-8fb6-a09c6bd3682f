{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Patient Meal Management</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Patient Meals</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'doctor_patient_meal_plans' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-clipboard-list mr-2"></i> View Meal Plans
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'doctor_create_meal_plan' %}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle mr-2"></i> Create Meal Plan
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'doctor_order_meal' %}" class="btn btn-info btn-block">
                                <i class="fas fa-utensils mr-2"></i> Order Meal
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3">
                            <a href="{% url 'doctor_meal_orders' %}" class="btn btn-warning btn-block">
                                <i class="fas fa-history mr-2"></i> View Orders
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Patients with Meal Plans -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Patients with Meal Plans</h5>
                </div>
                <div class="card-body">
                    {% if meal_plans %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0 datatable">
                            <thead>
                                <tr>
                                    <th>Patient</th>
                                    <th>Dietary Restrictions</th>
                                    <th>Calories/Day</th>
                                    <th>Created</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for plan in meal_plans %}
                                <tr>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="#">{{ plan.patient.name }}</a>
                                        </h2>
                                    </td>
                                    <td>{{ plan.get_dietary_restrictions_display }}</td>
                                    <td>{{ plan.calories_per_day }} kcal</td>
                                    <td>{{ plan.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <span class="badge {% if plan.is_active %}badge-success{% else %}badge-danger{% endif %}">
                                            {% if plan.is_active %}Active{% else %}Inactive{% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'doctor_meal_plan_detail' plan.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'doctor_order_meal' %}?patient={{ plan.patient.id }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-utensils"></i> Order Meal
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meal plans created yet. <a href="{% url 'doctor_create_meal_plan' %}">Create a meal plan</a> for your patients.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Meal Deliveries -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Today's Meal Deliveries</h5>
                </div>
                <div class="card-body">
                    {% if today_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in today_deliveries %}
                                <tr>
                                    <td>{{ delivery.meal.name }}</td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% elif delivery.status == 'in_transit' %}badge-primary
                                            {% elif delivery.status == 'delivered' %}badge-success
                                            {% elif delivery.status == 'consumed' %}badge-secondary
                                            {% elif delivery.status == 'returned' %}badge-danger
                                            {% elif delivery.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'doctor_meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meal deliveries scheduled for today.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Meal Deliveries -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Meal Deliveries</h5>
                </div>
                <div class="card-body">
                    {% if recent_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in recent_deliveries %}
                                <tr>
                                    <td>{{ delivery.meal.name }}</td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"M d, Y h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% elif delivery.status == 'in_transit' %}badge-primary
                                            {% elif delivery.status == 'delivered' %}badge-success
                                            {% elif delivery.status == 'consumed' %}badge-secondary
                                            {% elif delivery.status == 'returned' %}badge-danger
                                            {% elif delivery.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'doctor_meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No recent meal deliveries found.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .actions {
        display: flex;
        gap: 5px;
    }
</style>
{% endblock %}
