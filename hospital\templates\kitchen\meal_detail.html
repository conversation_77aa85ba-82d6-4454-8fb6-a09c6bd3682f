{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Meal Details</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_meals' %}">Manage Meals</a></li>
                        <li class="breadcrumb-item active" aria-current="page">{{ meal.name }}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meal Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Meal Name</label>
                                <div class="form-control-static">{{ meal.name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Meal Type</label>
                                <div class="form-control-static">{{ meal.get_meal_type_display }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Description</label>
                                <div class="form-control-static">{{ meal.description|default:"No description provided" }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Calories</label>
                                <div class="form-control-static">{{ meal.calories }} kcal</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Protein</label>
                                <div class="form-control-static">{{ meal.protein }} g</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Carbs</label>
                                <div class="form-control-static">{{ meal.carbs }} g</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Fat</label>
                                <div class="form-control-static">{{ meal.fat }} g</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12">
                            <div class="form-group">
                                <label>Preparation Instructions</label>
                                <div class="form-control-static">{{ meal.preparation_instructions|default:"No preparation instructions provided"|linebreaks }}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Meal Plan</label>
                                <div class="form-control-static">{{ meal.meal_plan.patient.name }} - {{ meal.meal_plan.get_dietary_restrictions_display }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Created At</label>
                                <div class="form-control-static">{{ meal.created_at|date:"M d, Y h:i A" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <a href="{% url 'edit_meal' meal.id %}" class="btn btn-primary btn-block">
                                <i class="fas fa-edit mr-2"></i> Edit Meal
                            </a>
                        </div>
                        <div class="col-md-12 mb-3">
                            <a href="{% url 'create_meal_delivery' %}?meal={{ meal.id }}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle mr-2"></i> Create Delivery
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">Nutrition Information</h5>
                </div>
                <div class="card-body">
                    <div class="nutrition-chart">
                        <canvas id="nutritionChart"></canvas>
                    </div>
                    <div class="nutrition-info mt-3">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="progress-item">
                                    <div class="progress-label">Protein</div>
                                    <div class="progress">
                                        <div class="progress-bar bg-success" role="progressbar" style="width: {{ meal.protein_percentage }}%" aria-valuenow="{{ meal.protein_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-value">{{ meal.protein }} g</div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="progress-item">
                                    <div class="progress-label">Carbs</div>
                                    <div class="progress">
                                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ meal.carbs_percentage }}%" aria-valuenow="{{ meal.carbs_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-value">{{ meal.carbs }} g</div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="progress-item">
                                    <div class="progress-label">Fat</div>
                                    <div class="progress">
                                        <div class="progress-bar bg-warning" role="progressbar" style="width: {{ meal.fat_percentage }}%" aria-valuenow="{{ meal.fat_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                                    </div>
                                    <div class="progress-value">{{ meal.fat }} g</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Deliveries</h5>
                </div>
                <div class="card-body">
                    {% if recent_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in recent_deliveries %}
                                <tr>
                                    <td>{{ delivery.id }}</td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"M d, Y h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% elif delivery.status == 'in_transit' %}badge-primary
                                            {% elif delivery.status == 'delivered' %}badge-success
                                            {% elif delivery.status == 'consumed' %}badge-secondary
                                            {% elif delivery.status == 'returned' %}badge-danger
                                            {% elif delivery.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No recent deliveries found for this meal.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .form-control-static {
        padding-top: 7px;
        padding-bottom: 7px;
        margin-bottom: 0;
        min-height: 34px;
    }
    
    .nutrition-chart {
        height: 200px;
    }
    
    .progress-item {
        margin-bottom: 15px;
    }
    
    .progress-label {
        margin-bottom: 5px;
        font-weight: 600;
    }
    
    .progress-value {
        text-align: right;
        margin-top: 5px;
        font-size: 12px;
        color: #666;
    }
</style>

{% block extrajs %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var ctx = document.getElementById('nutritionChart').getContext('2d');
        var nutritionChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: ['Protein', 'Carbs', 'Fat'],
                datasets: [{
                    data: [{{ meal.protein }}, {{ meal.carbs }}, {{ meal.fat }}],
                    backgroundColor: [
                        '#28a745',
                        '#007bff',
                        '#ffc107'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    });
</script>
{% endblock %}
{% endblock %}
