# Hospital Management System Environment Configuration
# Copy this file to .env and update the values according to your setup

# =============================================================================
# DJANGO SETTINGS
# =============================================================================
SECRET_KEY=django-insecure-change-this-to-a-secure-random-string-in-production
DEBUG=False
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0,your-domain.com

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Options: sqlite3, postgresql, mysql
DATABASE_ENGINE=postgresql

# PostgreSQL Settings (when DATABASE_ENGINE=postgresql)
DB_NAME=hospital_db
DB_USER=postgres
DB_PASSWORD=secure_postgres_password
DB_HOST=db
DB_PORT=5432

# MySQL Settings (when DATABASE_ENGINE=mysql)
# DB_NAME=hospital_db
# DB_USER=hospital_user
# DB_PASSWORD=secure_mysql_password
# DB_ROOT_PASSWORD=secure_root_password
# DB_HOST=db
# DB_PORT=3306

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://:redis_password@redis:6379/1
REDIS_PASSWORD=secure_redis_password

# =============================================================================
# SUPERUSER CONFIGURATION
# =============================================================================
DJANGO_SUPERUSER_USERNAME=admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=secure_admin_password

# =============================================================================
# EMAIL CONFIGURATION
# =============================================================================
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,https://your-domain.com

# =============================================================================
# DEPLOYMENT OPTIONS
# =============================================================================
# Set to true to load initial data from hmsk.sql (SQLite only)
LOAD_INITIAL_DATA=false

# =============================================================================
# SSL CONFIGURATION (for production)
# =============================================================================
# SECURE_SSL_REDIRECT=True
# SECURE_PROXY_SSL_HEADER=HTTP_X_FORWARDED_PROTO,https
# SESSION_COOKIE_SECURE=True
# CSRF_COOKIE_SECURE=True

# =============================================================================
# MONITORING AND LOGGING
# =============================================================================
# SENTRY_DSN=https://<EMAIL>/project-id

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
