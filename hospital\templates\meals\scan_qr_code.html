{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Scan QR Code</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'meal_dashboard' %}">Meal Tracking</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Scan QR Code</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Scan Meal QR Code</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div id="scanner-container" class="mx-auto" style="max-width: 400px;">
                            <div id="video-container" class="mb-3">
                                <video id="qr-video" style="width: 100%; border-radius: 8px;"></video>
                            </div>
                            <button id="start-button" class="btn btn-primary mb-3">
                                <i class="fas fa-camera mr-2"></i> Start Camera
                            </button>
                            <div id="scan-result" class="alert alert-info" style="display: none;"></div>
                        </div>
                    </div>
                    
                    <div class="text-center mb-4">
                        <h5>OR</h5>
                    </div>
                    
                    <form method="post" action="{% url 'scan_qr_code' %}">
                        {% csrf_token %}
                        <div class="form-group">
                            <label>Enter QR Code Manually</label>
                            <input type="text" name="qr_code" class="form-control" placeholder="Enter QR code value">
                        </div>
                        <button type="submit" class="btn btn-primary btn-block">
                            <i class="fas fa-search mr-2"></i> Find Meal
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="card-title">Instructions</h5>
                </div>
                <div class="card-body">
                    <ol>
                        <li>Click "Start Camera" to activate your device's camera</li>
                        <li>Point the camera at a meal QR code</li>
                        <li>The system will automatically detect and process the QR code</li>
                        <li>If you cannot scan the QR code, enter it manually in the form above</li>
                    </ol>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle mr-2"></i> Make sure you have given camera permissions to this website.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extrajs %}
<script src="https://unpkg.com/html5-qrcode@2.0.9/dist/html5-qrcode.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const videoElem = document.getElementById('qr-video');
        const startButton = document.getElementById('start-button');
        const scanResult = document.getElementById('scan-result');
        let scanner = null;
        
        startButton.addEventListener('click', function() {
            if (scanner) {
                scanner.stop();
                startButton.innerHTML = '<i class="fas fa-camera mr-2"></i> Start Camera';
                startButton.classList.remove('btn-danger');
                startButton.classList.add('btn-primary');
                return;
            }
            
            scanner = new Html5Qrcode("video-container");
            const config = { fps: 10, qrbox: 250 };
            
            scanner.start(
                { facingMode: "environment" },
                config,
                onScanSuccess,
                onScanFailure
            ).then(() => {
                startButton.innerHTML = '<i class="fas fa-stop mr-2"></i> Stop Camera';
                startButton.classList.remove('btn-primary');
                startButton.classList.add('btn-danger');
            }).catch((err) => {
                console.error('Error starting scanner:', err);
                alert('Could not start camera. Please check permissions and try again.');
                scanner = null;
            });
        });
        
        function onScanSuccess(decodedText, decodedResult) {
            // Stop scanning
            if (scanner) {
                scanner.stop();
                scanner = null;
                startButton.innerHTML = '<i class="fas fa-camera mr-2"></i> Start Camera';
                startButton.classList.remove('btn-danger');
                startButton.classList.add('btn-primary');
            }
            
            // Show result
            scanResult.style.display = 'block';
            scanResult.innerHTML = '<i class="fas fa-check-circle mr-2"></i> QR Code detected! Redirecting...';
            scanResult.classList.remove('alert-info', 'alert-danger');
            scanResult.classList.add('alert-success');
            
            // Redirect to meal delivery page
            setTimeout(() => {
                window.location.href = `/meals/delivery/${decodedText}/`;
            }, 1000);
        }
        
        function onScanFailure(error) {
            // Handle scan failure silently
            console.warn(`QR scan error: ${error}`);
        }
    });
</script>
{% endblock %}
{% endblock %}
