{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">

    <div class="page-header">
    <div class="row align-items-center">
    <div class="col">
    <h3 class="page-title">Appointments</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">View Aappointment</li>
    </ul>
    </div>
    
    </div>
    </div>
    
    <div class="row">
    <div class="col-sm-12">
    <div class="card card-table">
    <div class="card-body">
        {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'error' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
           {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'success' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
    <div class="table-responsive">
    <table class="table table-hover table-center mb-0 datatable">
      {% for i in patientdetails %}
      <tr><label style="font-size: medium;text-align: center;">Appointment Number: {{i.appointmentnumber}}</label></tr>
        <tr>
            
            <th>Patient Name</th>
            <td>{{i.pat_id.admin.first_name}} {{i.pat_id.admin.last_name}}</td>
            <th>Patient Contact Number</th>
            <td>{{i.pat_id.mobilenumber}}</td>

          </tr>
          <tr>
            
            <th>Patient Address</th>
            <td>{{i.pat_id.address}}</td>
            <th>Patient Gender</th>
            <td>{{i.pat_id.gender}}</td>
          </tr>
          <tr>
            
            <th>Patient Email</th>
            <td>{{i.pat_id.admin.email}}</td>
            <th>Date of Appointment</th>
            <td>{{i.date_of_appointment}}</td>
          </tr>
          <tr>
            
            <th>Date of Time</th>
            <td>{{i.time_of_appointment}}</td>
            <th>Message</th>
            <td>{{i.additional_msg}}</td>
          </tr>
          <tr>
                              
            {% if i.status == '0' %}
            <th>Doctor Remark</th>
            <td>Not Updatet Yet</td>
            {% else %}
            <tr>
            <th>Doctor Remark</th>
            <td>{{ i.remark}}</td>{% endif %}
            
              {% if i.status == '0' %}
              <th>Staus</th>
              <td>Not Updatet Yet</td>
              {% else %}
              <th>Status</th>
              <td>{{ i.status}}</td> {% endif %}
              </tr>
          
            
             
    </table>
    {% if i.status == '0'  %}
    
              </div>
              <p align="center"  style="padding-top: 20px">                            
                <button class="btn btn-primary waves-effect waves-light w-lg" data-toggle="modal" data-target="#myModal">Take Action</button></p>{% endif %}  
              <div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                <div class="modal-dialog" role="document">
                   <div class="modal-content">
                    <div class="modal-header">
                                                                  <h5 class="modal-title" id="exampleModalLabel">Take Action</h5>
                                                                  <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                      <span aria-hidden="true">&times;</span>
                                                                  </button>
                                                              </div>
                                                              <div class="modal-body">
                                                              <table class="table table-bordered table-hover data-tables">
              
                                               <form method="POST" action="{% url 'patient_appointment_details_remark'%}">
                                                {% csrf_token %}
              
                                              
                                             
                   <tr>
                  <th>Remark :</th>
                  <td>
                  <textarea name="remark" placeholder="Remark" rows="12" cols="14" class="form-control wd-450" required="true"></textarea></td>
                  <input type="text" value="{% for i in patientdetails %}{{i.id}}{% endfor %}" name="pat_id" id="pat_id" class="form-control" required="" hidden>
                </tr> 
                   
                <tr>
                  <th>Status :</th>
                  <td>
              
                 <select name="status" class="form-control wd-450" required="true" >
                   <option value="Approved" selected="true">Approved</option>
                   <option value="Canceled">Canceled</option>
                   
                 </select></td>
                </tr>
              </table>
              </div>
              <div class="modal-footer">
               <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
               <button type="submit" name="submit" class="btn btn-primary">Update</button>
                
                </form>
                
              
              </div>
              
                                    
                                      </div>
                                  </div>
              
                          </div>{% endfor %}
              
                        </div>
    

    </div>
    </div>
    </div>
    </div>
    </div>
    </div>


{% endblock %}