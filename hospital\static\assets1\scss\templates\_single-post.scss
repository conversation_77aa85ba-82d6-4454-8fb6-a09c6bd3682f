/*=================================================================
  Single Blog Page
==================================================================*/

.nav-links{
  .page-numbers{
    display: inline-block;
    width: 50px;
    height: 50px;
    border-radius: 100%;
    background: #eee;
    text-align: center;
    padding-top: 13px;
    font-weight: 600;
    margin-right: 10px;

    &:hover{
      background: $primary-color;
      color: $light;
    }
    &.current{
      background: $primary-color;
      color: $light;
    }
  }


}

//  Comments

.comment-area{

  .comment-thumb{
    margin-right: 20px;
    margin-bottom: 30px;
  }
  
  h5{
    font-size: 18px;
    font-weight: 500;
  }
  span{
    font-size: 14px;
  }
}


.posts-nav {
  h6{
    font-weight: 500;
  }
}




.quote{
  font-size: 22px;
  color: $title-color;
  padding: 40px;
  font-style: italic;
  border-left:5px solid $secondary-color;
  margin: 25px 0px;
}

.tag-option{
  a{
    border:1px solid $gray;
    padding: 6px 12px;
    color: $base-color;
    font-size: 14px;
  }
}







// Comment Form
.comment-form {
  .form-control{
    background: #f7f8fb;
    border-radius:5px;
    border-color:#f7f8fb;
    height: 50px;
  }

  textarea.form-control{
    height:auto;
  }
}












.post.post-single {
  border:none;
  .post-thumb {
    margin-top:30px;
  }
}
.post-sub-heading {
  border-bottom:1px solid #dedede;
  padding-bottom:20px;
  letter-spacing: 2px;
  text-transform: uppercase;
  font-size: 16px;
  margin-bottom:20px;
}
.post-social-share {
  margin-bottom:50px;
}

.post-comments {
  margin:30px 0;
  .media {
    margin-top:20px;
    > .pull-left {
      padding-right: 20px;
    }
  }
  .comment-author {
    margin-top: 0;
    margin-bottom:0px;
    font-weight:500;
    a {
      color: $primary-color;
      font-size: 14px;
      text-transform: uppercase;
    }
  }
  time {
    margin:0 0 5px;
    display: inline-block;
    color: #808080;
    font-size:12px;
  }
  .comment-button {
    color: $primary-color;
    display: inline-block;
    margin-left:5px;
    font-size:12px;
    i {
      margin-right:5px;
      display: inline-block;
    }
    &:hover {
      color: $primary-color;
    }
  }
}

.post-excerpt {
  margin-bottom: 60px;
  h3 {
    a {
      color: #000;
    }
  }
  p {
    margin: 0 0 30px;
  }
  blockquote.quote-post {
    margin: 20px 0;
    p {
      line-height: 30px;
      font-size: 20px;
      color:$primary-color;
    }
  }
}



.comments-section {
  margin-top: 35px;
}


.author-about {
  margin-top: 40px;
}
.post-author {
  margin-right: 20px;
}

.post-author > img {
  border: 1px solid #dedede;
  max-width: 120px;
  padding: 5px;
  width: 100%;
}



.comment-list {
  ul {
    margin-top: 20px;
    li {
      margin-bottom: 20px;
    }
  }
}


.comment-wrap {
  border: 1px solid #dedede;
  border-radius: 1px;
  margin-left: 20px;
  padding: 10px;
  position: relative;
  .author-avatar {
    margin-right: 10px;
  }
  .media {
    .media-heading {
      font-size: 14px;
      margin-bottom: 8px;
      a {
        color: $primary-color;
        font-size: 13px;
      }
    }
    .comment-meta {
      font-size: 12px;
      color: #888;
    }
    p {
      margin-top: 15px;
    }
  }

}


.comment-reply-form {
  margin-top: 80px;
  input,textarea {
    height: 35px;
    border-radius: 0;
    box-shadow: none;
    &:focus {
      box-shadow:none;
      border:1px solid $primary-color;
    }
  }
  textarea,.btn-main {
    height: auto;
  }
}

                            



