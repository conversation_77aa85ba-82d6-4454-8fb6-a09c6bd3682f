{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">
    {% if user.user_type == '1' %}
    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Add Patient</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Add Patient</li>
    </ul>
    </div>
    </div>
    </div>
    {% else  %}
    <div class="page-header">
        <div class="row">
        <div class="col">
        <h3 class="page-title">Add Patient</h3>
        <ul class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
        <li class="breadcrumb-item active">Add Patient</li>
        </ul>
        </div>
        </div>
        </div>{% endif %}
    
    <div class="row">
    <div class="col-lg-12">
    <div class="card">
    <div class="card-header">
    <h5 class="card-title">Add Patient</h5>
    </div>
    <div class="card-body">
        {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'error' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                           {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'success' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                       <form method="POST" action="">
                        {% csrf_token %}
   
    <div class="form-group row">
    <label class="col-form-label col-md-2">Full Name</label>
    <div class="col-md-10">
        <input type="text" class="form-control" name="name" required="True">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Mobile Number</label>
    <div class="col-md-10">
        <input type="text" class="form-control" name="mobilenumber" maxlength="10" pattern="[0-9]+" required="True">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Email</label>
    <div class="col-md-10">
        <input type="email" class="form-control" name="email" required="True">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Gender</label>
    <div class="col-md-10">
        <select id="gender" class="form-control" name="gender" required="true">
            <option value="">Choose Gender</option>
            <option value="Male">Male</option>
            <option value="Female">Female</option>
        </select>
    </div>
    </div>
    <div class="form-group row">
        <label class="col-form-label col-md-2">Address</label>
        <div class="col-md-10">
            <textarea class="form-control" name="address"  required="True"></textarea>
        </div>
        </div>
        <div class="form-group row">
            <label class="col-form-label col-md-2">Age</label>
            <div class="col-md-10">
                <input type="text" class="form-control" name="age" required="True">
            </div>
            </div>
            <div class="form-group row">
    <label class="col-form-label col-md-2">Medical History(if any)</label>
    <div class="col-md-10">
        <textarea class="form-control" rows="6" name="medhistory"></textarea>
    </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-10">
            <button type="submit" class="btn btn-dark">Add</button>
        </div>
    </div>
    
    </form>
    </div>
    </div>

    </div>
    </div>
    </div>



{% endblock %}