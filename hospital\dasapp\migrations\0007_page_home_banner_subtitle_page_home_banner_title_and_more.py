# Generated by Django 5.2 on 2025-04-10 15:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dasapp', '0006_alter_customuser_user_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='page',
            name='home_banner_subtitle',
            field=models.CharField(default='Total Health care solution', max_length=100),
        ),
        migrations.AddField(
            model_name='page',
            name='home_banner_title',
            field=models.CharField(default='Your most trusted health partner', max_length=100),
        ),
        migrations.AddField(
            model_name='page',
            name='home_feature1_text',
            field=models.TextField(default='Get ALl time support for emergency.We have introduced the principle of family medicine.'),
        ),
        migrations.AddField(
            model_name='page',
            name='home_feature1_title',
            field=models.CharField(default='Online Appoinment', max_length=50),
        ),
        migrations.AddField(
            model_name='page',
            name='home_feature2_hours',
            field=models.TextField(default='Sun - Wed : 8:00 - 17:00\nThu - Fri : 9:00 - 17:00\nSat - sun : 10:00 - 17:00', help_text='Enter each day/time on a new line.'),
        ),
        migrations.AddField(
            model_name='page',
            name='home_feature2_title',
            field=models.CharField(default='Working Hours', max_length=50),
        ),
        migrations.AddField(
            model_name='page',
            name='home_feature3_text',
            field=models.TextField(default='Get ALl time support for emergency.We have introduced the principle of family medicine.Get Conneted with us for any urgency .'),
        ),
        migrations.AddField(
            model_name='page',
            name='home_feature3_title',
            field=models.CharField(default='1-************', max_length=50),
        ),
        migrations.AddField(
            model_name='page',
            name='home_service_title',
            field=models.CharField(default='Award winning patient care', max_length=100),
        ),
        migrations.AlterField(
            model_name='customuser',
            name='user_type',
            field=models.CharField(choices=[(2, 'doc'), (1, 'admin'), (4, 'kitchen'), (3, 'patient')], default=1, max_length=50),
        ),
        migrations.AlterField(
            model_name='page',
            name='aboutus',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='page',
            name='address',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
        migrations.AlterField(
            model_name='page',
            name='email',
            field=models.EmailField(blank=True, max_length=200, null=True),
        ),
        migrations.AlterField(
            model_name='page',
            name='mobilenumber',
            field=models.IntegerField(blank=True, default=0, null=True),
        ),
        migrations.AlterField(
            model_name='page',
            name='pagetitle',
            field=models.CharField(blank=True, max_length=250, null=True),
        ),
    ]
