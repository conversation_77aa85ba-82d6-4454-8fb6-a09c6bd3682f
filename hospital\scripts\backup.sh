#!/bin/bash
# Database backup script for Hospital Management System

set -e

# Configuration
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME=${DB_NAME:-hospital_db}
DB_USER=${DB_USER:-postgres}
DB_HOST=${DB_HOST:-db}

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create backup directory if it doesn't exist
mkdir -p "$BACKUP_DIR"

# PostgreSQL backup
if [ "$DATABASE_ENGINE" = "postgresql" ]; then
    BACKUP_FILE="$BACKUP_DIR/hospital_db_backup_$DATE.sql"
    print_status "Creating PostgreSQL backup: $BACKUP_FILE"
    
    pg_dump -h "$DB_HOST" -U "$DB_USER" -d "$DB_NAME" > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        print_status "PostgreSQL backup completed successfully"
        gzip "$BACKUP_FILE"
        print_status "Backup compressed: $BACKUP_FILE.gz"
    else
        print_error "PostgreSQL backup failed"
        exit 1
    fi

# MySQL backup
elif [ "$DATABASE_ENGINE" = "mysql" ]; then
    BACKUP_FILE="$BACKUP_DIR/hospital_db_backup_$DATE.sql"
    print_status "Creating MySQL backup: $BACKUP_FILE"
    
    mysqldump -h "$DB_HOST" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" > "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        print_status "MySQL backup completed successfully"
        gzip "$BACKUP_FILE"
        print_status "Backup compressed: $BACKUP_FILE.gz"
    else
        print_error "MySQL backup failed"
        exit 1
    fi

# SQLite backup
else
    BACKUP_FILE="$BACKUP_DIR/hospital_db_backup_$DATE.sqlite3"
    print_status "Creating SQLite backup: $BACKUP_FILE"
    
    cp /app/db.sqlite3 "$BACKUP_FILE"
    
    if [ $? -eq 0 ]; then
        print_status "SQLite backup completed successfully"
        gzip "$BACKUP_FILE"
        print_status "Backup compressed: $BACKUP_FILE.gz"
    else
        print_error "SQLite backup failed"
        exit 1
    fi
fi

# Clean up old backups (keep last 30 days by default)
RETENTION_DAYS=${BACKUP_RETENTION_DAYS:-30}
print_status "Cleaning up backups older than $RETENTION_DAYS days"
find "$BACKUP_DIR" -name "hospital_db_backup_*.gz" -mtime +$RETENTION_DAYS -delete

print_status "Backup process completed successfully"
