/*
Author       : Augment Agent
Template Name: Hospital Management System with Kitchen - Modern UI
Version      : 2.0
*/

/*============================
 [Table of CSS]

1. General
2. Variables
3. Layout
4. Header
5. Sidebar
6. Cards
7. Tables
8. Forms
9. Buttons
10. Utilities
11. Responsive
========================================*/

/*-----------------
    1. General
-----------------------*/
:root {
    --primary-color: #4361ee;
    --primary-light: #eaefff;
    --secondary-color: #3f37c9;
    --success-color: #4cc9f0;
    --info-color: #4895ef;
    --warning-color: #f72585;
    --danger-color: #e63946;
    --dark-color: #1d3557;
    --light-color: #f8f9fa;
    --gray-color: #6c757d;
    --gray-light: #e9ecef;
    --gray-dark: #343a40;
    --body-bg: #f5f7fb;
    --sidebar-bg: #ffffff;
    --header-bg: #ffffff;
    --card-bg: #ffffff;
    --border-color: #e9ecef;
    --text-color: #333;
    --text-muted: #6c757d;
    --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
    --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.1);
    --radius-sm: 0.25rem;
    --radius: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --transition: all 0.3s ease;
    --header-height: 70px;
    --sidebar-width: 260px;
    --sidebar-mini-width: 80px;
}

html {
    height: 100%;
    scroll-behavior: smooth;
}

body {
    background-color: var(--body-bg);
    color: var(--text-color);
    font-family: 'Poppins', sans-serif;
    font-size: 0.95rem;
    height: 100%;
    line-height: 1.5;
    overflow-x: hidden;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
    text-decoration: none;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--dark-color);
    font-weight: 600;
    margin-bottom: 1rem;
}

p {
    margin-bottom: 1rem;
}

/*-----------------
    3. Layout
-----------------------*/
.main-wrapper {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.page-wrapper {
    background-color: var(--body-bg);
    flex: 1;
    margin-left: var(--sidebar-width);
    padding: 1.5rem;
    transition: var(--transition);
}

.mini-sidebar .page-wrapper {
    margin-left: var(--sidebar-mini-width);
}

.content {
    padding: 1.5rem;
}

.container-fluid {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
}

/*-----------------
    4. Header
-----------------------*/
.header {
    background-color: var(--header-bg);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    height: var(--header-height);
    left: 0;
    position: fixed;
    right: 0;
    top: 0;
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 1.5rem;
}

.header-left {
    display: flex;
    align-items: center;
}

.header-left .logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-right: 1rem;
}

.header-left .logo img {
    max-height: 40px;
    margin-right: 0.5rem;
}

.header-left .logo strong {
    color: var(--primary-color);
    font-weight: 700;
}

.top-nav-search {
    margin-left: 1.5rem;
}

.top-nav-search h4 {
    color: var(--primary-color);
    font-size: 1.25rem;
    margin-bottom: 0;
}

.user-menu {
    margin-left: auto;
    display: flex;
    align-items: center;
}

.user-menu .nav-item {
    margin-left: 1rem;
}

.user-menu .dropdown-toggle {
    display: flex;
    align-items: center;
    padding: 0;
}

.user-menu .user-img {
    display: inline-block;
    position: relative;
}

.user-menu .user-img img {
    border: 2px solid var(--primary-light);
    height: 40px;
    width: 40px;
    object-fit: cover;
}

.user-menu .dropdown-menu {
    min-width: 200px;
    padding: 0;
    border: none;
    box-shadow: var(--shadow);
    border-radius: var(--radius);
    overflow: hidden;
}

.user-header {
    background-color: var(--primary-light);
    padding: 1rem;
    display: flex;
    align-items: center;
}

.user-header .avatar {
    margin-right: 1rem;
}

.user-header .avatar img {
    height: 40px;
    width: 40px;
    object-fit: cover;
}

.user-header .user-text h6 {
    margin-bottom: 0.25rem;
    color: var(--primary-color);
}

.user-header .user-text p {
    margin-bottom: 0;
    font-size: 0.85rem;
}

.dropdown-menu .dropdown-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: var(--transition);
}

.dropdown-menu .dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-menu .dropdown-item:hover {
    background-color: var(--primary-light);
    color: var(--primary-color);
}

.mobile_btn {
    display: none;
    float: left;
    font-size: 24px;
    height: 60px;
    line-height: 60px;
    margin-right: 15px;
    padding: 0 15px;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
    z-index: 10;
    color: var(--primary-color);
}

/*-----------------
    5. Sidebar
-----------------------*/
.sidebar {
    background-color: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    bottom: 0;
    left: 0;
    margin-top: var(--header-height);
    position: fixed;
    top: 0;
    transition: var(--transition);
    width: var(--sidebar-width);
    z-index: 999;
    box-shadow: var(--shadow-sm);
    overflow-y: auto;
}

.sidebar-inner {
    height: 100%;
    transition: var(--transition);
}

.sidebar-menu {
    padding: 1rem 0;
}

.sidebar-menu ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.sidebar-menu li {
    position: relative;
}

.sidebar-menu li a {
    color: var(--text-color);
    display: flex;
    align-items: center;
    font-size: 0.95rem;
    height: 45px;
    padding: 0 20px;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.sidebar-menu li a:hover {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-left-color: var(--primary-color);
}

.sidebar-menu li.active a {
    color: var(--primary-color);
    background-color: var(--primary-light);
    border-left-color: var(--primary-color);
}

.sidebar-menu li a i {
    font-size: 1.1rem;
    margin-right: 10px;
    text-align: center;
    width: 20px;
}

.sidebar-menu li.menu-title {
    color: var(--text-muted);
    font-size: 0.85rem;
    font-weight: 600;
    margin: 1.5rem 0 0.5rem;
    padding: 0 20px;
    text-transform: uppercase;
}

.sidebar-menu .submenu {
    display: none;
    list-style: none;
    margin: 0;
    padding: 0;
}

.sidebar-menu .submenu li a {
    padding-left: 50px;
}

.sidebar-menu .submenu .submenu li a {
    padding-left: 70px;
}

.sidebar-menu .menu-arrow {
    display: inline-block;
    font-size: 1rem;
    margin-left: auto;
    transition: var(--transition);
}

.sidebar-menu li.submenu-open > a .menu-arrow {
    transform: rotate(90deg);
}

.sidebar-menu li.submenu-open > .submenu {
    display: block;
}

.mini-sidebar .sidebar {
    width: var(--sidebar-mini-width);
}

.mini-sidebar .sidebar .sidebar-menu li a {
    padding: 0;
    justify-content: center;
}

.mini-sidebar .sidebar .sidebar-menu li a i {
    margin: 0;
    font-size: 1.5rem;
}

.mini-sidebar .sidebar .sidebar-menu li a span,
.mini-sidebar .sidebar .sidebar-menu li.menu-title,
.mini-sidebar .sidebar .sidebar-menu li a .menu-arrow {
    display: none;
}

.mini-sidebar .sidebar .sidebar-menu .submenu {
    display: none !important;
}

/*-----------------
    6. Cards
-----------------------*/
.card {
    border: none;
    box-shadow: var(--shadow);
    border-radius: var(--radius);
    margin-bottom: 1.5rem;
    transition: var(--transition);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-5px);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

.card-title {
    color: var(--dark-color);
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    background-color: transparent;
    border-top: 1px solid var(--border-color);
    padding: 1rem 1.5rem;
}

.card-bg-primary {
    background-color: var(--primary-color);
    color: white;
}

.card-bg-success {
    background-color: var(--success-color);
    color: white;
}

.card-bg-info {
    background-color: var(--info-color);
    color: white;
}

.card-bg-warning {
    background-color: var(--warning-color);
    color: white;
}

.card-bg-danger {
    background-color: var(--danger-color);
    color: white;
}

.card-bg-primary .card-title,
.card-bg-success .card-title,
.card-bg-info .card-title,
.card-bg-warning .card-title,
.card-bg-danger .card-title {
    color: white;
}

.db-widgets {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.db-icon {
    font-size: 2.5rem;
    opacity: 0.8;
}

.db-info h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.db-info h6 {
    font-size: 1rem;
    margin-bottom: 0;
    opacity: 0.8;
}

/*-----------------
    7. Tables
-----------------------*/
.table {
    color: var(--text-color);
    margin-bottom: 0;
}

.table thead th {
    border-bottom: 1px solid var(--border-color);
    color: var(--dark-color);
    font-weight: 600;
    padding: 1rem;
}

.table tbody td {
    border-top: 1px solid var(--border-color);
    padding: 1rem;
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: var(--primary-light);
}

.table-center td, .table-center th {
    text-align: center;
}

.table-avatar {
    align-items: center;
    display: inline-flex;
    font-size: 0.95rem;
    font-weight: 500;
    margin: 0;
    white-space: nowrap;
}

.table-avatar a {
    color: var(--dark-color);
}

.table-avatar a:hover {
    color: var(--primary-color);
}

.table-avatar .avatar {
    margin-right: 0.75rem;
}

.table-responsive {
    white-space: nowrap;
}

/*-----------------
    8. Forms
-----------------------*/
.form-group {
    margin-bottom: 1.25rem;
}

.form-control {
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    color: var(--text-color);
    height: 45px;
    padding: 0.375rem 0.75rem;
    transition: var(--transition);
}

.form-control:focus {
    background-color: #fff;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

.form-control-sm {
    height: 35px;
    padding: 0.25rem 0.5rem;
}

.form-control-lg {
    height: 55px;
    padding: 0.5rem 1rem;
}

textarea.form-control {
    height: auto;
}

.form-label {
    color: var(--dark-color);
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    color: var(--text-muted);
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

.form-check-input {
    margin-top: 0.3rem;
}

.form-check-label {
    margin-bottom: 0;
}

.form-select {
    background-color: var(--light-color);
    border: 1px solid var(--border-color);
    border-radius: var(--radius);
    color: var(--text-color);
    height: 45px;
    padding: 0.375rem 2.25rem 0.375rem 0.75rem;
    transition: var(--transition);
}

.form-select:focus {
    background-color: #fff;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(67, 97, 238, 0.25);
}

/*-----------------
    9. Buttons
-----------------------*/
.btn {
    border-radius: var(--radius);
    font-size: 0.95rem;
    font-weight: 500;
    padding: 0.5rem 1.5rem;
    transition: var(--transition);
}

.btn-sm {
    font-size: 0.85rem;
    padding: 0.25rem 0.75rem;
}

.btn-lg {
    font-size: 1.1rem;
    padding: 0.75rem 2rem;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border-color: var(--secondary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-outline-primary {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: #fff;
}

.btn-block {
    width: 100%;
}

.btn i {
    margin-right: 0.5rem;
}

/*-----------------
    10. Utilities
-----------------------*/
.badge {
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.35em 0.65em;
}

.badge-primary {
    background-color: var(--primary-color);
}

.badge-secondary {
    background-color: var(--secondary-color);
}

.badge-success {
    background-color: var(--success-color);
}

.badge-info {
    background-color: var(--info-color);
}

.badge-warning {
    background-color: var(--warning-color);
}

.badge-danger {
    background-color: var(--danger-color);
}

.text-primary {
    color: var(--primary-color) !important;
}

.text-secondary {
    color: var(--secondary-color) !important;
}

.text-success {
    color: var(--success-color) !important;
}

.text-info {
    color: var(--info-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-muted {
    color: var(--text-muted) !important;
}

.bg-primary {
    background-color: var(--primary-color) !important;
}

.bg-secondary {
    background-color: var(--secondary-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-info {
    background-color: var(--info-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-light {
    background-color: var(--light-color) !important;
}

.bg-dark {
    background-color: var(--dark-color) !important;
}

.avatar {
    position: relative;
    display: inline-block;
}

.avatar-img {
    height: 100%;
    width: 100%;
    object-fit: cover;
}

.avatar-xs {
    height: 1.5rem;
    width: 1.5rem;
}

.avatar-sm {
    height: 2rem;
    width: 2rem;
}

.avatar-md {
    height: 3rem;
    width: 3rem;
}

.avatar-lg {
    height: 4rem;
    width: 4rem;
}

.avatar-xl {
    height: 5rem;
    width: 5rem;
}

.rounded-circle {
    border-radius: 50% !important;
}

.page-header {
    margin-bottom: 1.5rem;
}

.page-title {
    color: var(--dark-color);
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.breadcrumb {
    background-color: transparent;
    margin-bottom: 0;
    padding: 0;
}

.breadcrumb-item + .breadcrumb-item::before {
    color: var(--text-muted);
}

.breadcrumb-item a {
    color: var(--primary-color);
}

.breadcrumb-item.active {
    color: var(--text-muted);
}

/*-----------------
    11. Responsive
-----------------------*/
@media (max-width: 991.98px) {
    .header {
        padding: 0 1rem;
    }
    
    .page-wrapper {
        margin-left: 0;
        padding: 1rem;
    }
    
    .sidebar {
        margin-left: -260px;
    }
    
    .sidebar.opened {
        margin-left: 0;
    }
    
    .mobile_btn {
        display: block;
    }
}

@media (max-width: 767.98px) {
    .header-left .logo span {
        display: none;
    }
    
    .user-menu .dropdown-menu {
        position: fixed !important;
        top: var(--header-height) !important;
        left: 0 !important;
        right: 0 !important;
        width: 100%;
        transform: none !important;
    }
}

@media (max-width: 575.98px) {
    .header {
        height: 60px;
    }
    
    .header-left .logo img {
        max-height: 30px;
    }
    
    .top-nav-search h4 {
        font-size: 1rem;
    }
    
    .user-menu .user-img img {
        height: 30px;
        width: 30px;
    }
    
    .page-title {
        font-size: 1.25rem;
    }
}
