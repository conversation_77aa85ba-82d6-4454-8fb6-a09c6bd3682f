{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">My Meals</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'userhome' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">My Meals</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-sm-6 mb-3">
                            <a href="{% url 'patient_meal_history' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-history mr-2"></i> View Meal History
                            </a>
                        </div>
                        <div class="col-md-6 col-sm-6 mb-3">
                            <a href="#today-meals" class="btn btn-info btn-block">
                                <i class="fas fa-utensils mr-2"></i> Today's Meals
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- My Meal Plans -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">My Meal Plans</h5>
                </div>
                <div class="card-body">
                    {% if meal_plans %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Dietary Restrictions</th>
                                    <th>Calories/Day</th>
                                    <th>Protein</th>
                                    <th>Carbs</th>
                                    <th>Fat</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for plan in meal_plans %}
                                <tr>
                                    <td>{{ plan.get_dietary_restrictions_display }}</td>
                                    <td>{{ plan.calories_per_day }} kcal</td>
                                    <td>{{ plan.protein_per_day }} g</td>
                                    <td>{{ plan.carbs_per_day }} g</td>
                                    <td>{{ plan.fat_per_day }} g</td>
                                    <td>Dr. {{ plan.created_by.first_name }} {{ plan.created_by.last_name }}</td>
                                    <td>
                                        <a href="{% url 'patient_meal_plan_detail' plan.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">You don't have any meal plans yet. Your doctor will create one for you.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Meals -->
    <div class="row" id="today-meals">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Today's Meals</h5>
                </div>
                <div class="card-body">
                    {% if today_meals %}
                    <div class="row">
                        {% for meal in today_meals %}
                        <div class="col-md-6 col-lg-4">
                            <div class="card meal-card">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="card-title mb-0">{{ meal.meal.get_meal_type_display }} - {{ meal.scheduled_time|date:"h:i A" }}</h5>
                                </div>
                                <div class="card-body">
                                    <h4 class="meal-name">{{ meal.meal.name }}</h4>
                                    <p class="meal-description">{{ meal.meal.description|truncatechars:100 }}</p>
                                    
                                    <div class="meal-nutrition">
                                        <div class="nutrition-item">
                                            <span class="label">Calories:</span>
                                            <span class="value">{{ meal.meal.calories }} kcal</span>
                                        </div>
                                        <div class="nutrition-item">
                                            <span class="label">Protein:</span>
                                            <span class="value">{{ meal.meal.protein }} g</span>
                                        </div>
                                        <div class="nutrition-item">
                                            <span class="label">Carbs:</span>
                                            <span class="value">{{ meal.meal.carbs }} g</span>
                                        </div>
                                    </div>
                                    
                                    <div class="meal-status mt-3">
                                        <span class="badge 
                                            {% if meal.status == 'preparing' %}badge-warning
                                            {% elif meal.status == 'ready' %}badge-info
                                            {% elif meal.status == 'in_transit' %}badge-primary
                                            {% elif meal.status == 'delivered' %}badge-success
                                            {% elif meal.status == 'consumed' %}badge-secondary
                                            {% elif meal.status == 'returned' %}badge-danger
                                            {% elif meal.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ meal.get_status_display }}
                                        </span>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between">
                                        <a href="{% url 'patient_meal_delivery_detail' meal.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View Details
                                        </a>
                                        {% if meal.status == 'delivered' %}
                                        <a href="{% url 'patient_meal_feedback' meal.id %}" class="btn btn-sm btn-success">
                                            <i class="fas fa-comment"></i> Provide Feedback
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meals scheduled for today.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Upcoming Meals -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Upcoming Meals</h5>
                </div>
                <div class="card-body">
                    {% if upcoming_meals %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Time</th>
                                    <th>Meal Type</th>
                                    <th>Meal</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for meal in upcoming_meals %}
                                <tr>
                                    <td>{{ meal.scheduled_time|date:"M d, Y" }}</td>
                                    <td>{{ meal.scheduled_time|date:"h:i A" }}</td>
                                    <td>{{ meal.meal.get_meal_type_display }}</td>
                                    <td>{{ meal.meal.name }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if meal.status == 'preparing' %}badge-warning
                                            {% elif meal.status == 'ready' %}badge-info
                                            {% elif meal.status == 'in_transit' %}badge-primary
                                            {% elif meal.status == 'delivered' %}badge-success
                                            {% elif meal.status == 'consumed' %}badge-secondary
                                            {% elif meal.status == 'returned' %}badge-danger
                                            {% elif meal.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ meal.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'patient_meal_delivery_detail' meal.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i> View
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No upcoming meals scheduled.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .meal-card {
        height: 100%;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s;
    }
    
    .meal-card:hover {
        transform: translateY(-5px);
    }
    
    .meal-name {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 10px;
    }
    
    .meal-description {
        color: #666;
        margin-bottom: 15px;
    }
    
    .meal-nutrition {
        background-color: #f9f9f9;
        padding: 10px;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    
    .nutrition-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    
    .nutrition-item .label {
        font-weight: 600;
        color: #333;
    }
    
    .nutrition-item .value {
        color: #666;
    }
</style>
{% endblock %}
