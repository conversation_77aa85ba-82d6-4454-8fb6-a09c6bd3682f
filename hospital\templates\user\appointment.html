{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">
    {% if user.user_type == '1' %}
    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Book Appointment</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Book Appointment</li>
    </ul>
    </div>
    </div>
    </div>
   {% elif user.user_type == '2' %}
    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Profile Details</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Book Appointment</li>
    </ul>
    </div>
    </div>
    </div>
    {% else  %}
    <div class="page-header">
        <div class="row">
        <div class="col">
        <h3 class="page-title">Book Appointment</h3>
        <ul class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'userhome' %}">Dashboard</a></li>
        <li class="breadcrumb-item active">Book Appointment</li>
        </ul>
        </div>
        </div>
        </div>{% endif %}
    
    <div class="row">
    <div class="col-lg-12">
    <div class="card">
    <div class="card-header">
    <h5 class="card-title">Book Appointment</h5>
    </div>
    <div class="card-body">
        {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'error' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                           {% if messages %}
                        {% for message in messages %}
                         {% if message.tags == 'success' %}
                       <div class="alert alert-warning alert-dismissible fade show" role="alert">
                      {{message}}
                     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                     <span aria-hidden="true">&times;</span>
                         </button>
                          </div>
                       {% endif %}
                        {% endfor %}
                       {% endif %}
                       <form method="POST" action="{% url 'patientappointment' %}" enctype="multipart/form-data">
                        {% csrf_token %}
    <div class="form-group row">
    <label class="col-form-label col-md-2">Doctor Specialization</label>
    <div class="col-md-10">
      
        <select name="spec_id" class="form-control" required="true" id="specialization">
              
            <option value="">Select Specializations</option>
            {% for i in specialization %}
             
            <option value="{{i.id}}">{{i.sname}}</option>{% endfor %}
          </select>
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Doctor</label>
    <div class="col-md-10">
        
        <select class="form-control" name="doctor_id" id="doctor" required="required">
            <option value="">Select Doctor</option>
        </select>
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Date</label>
    <div class="col-md-10">
        <input type="date" class="form-control" name="date_of_appointment" required="True">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Time</label>
    <div class="col-md-10">
        <input type="time" class="form-control" required="True" name="time_of_appointment">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">Additional Message</label>
    <div class="col-md-10">
        <textarea class="form-control" name="additional_msg"></textarea>
    </div>
    </div>
    <div class="form-group row">
        <div class="col-sm-10">
            <button type="submit" class="btn btn-dark">Update</button>
        </div>
    </div>
    
    </form>
    </div>
    </div>

    </div>
    </div>
    </div>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
$(document).ready(function() {
    $('#specialization').change(function() {
        var sid = $(this).val();
        $.ajax({
            url: '/get_doctor/',
            type: 'GET',
            data: {
                's_id': sid
            },
            success: function(data) {
                var doctoroptions = data.doctor_options;
                $('#doctor').html(doctoroptions);
            },
            error: function(xhr, status, error) {
                console.error(xhr.responseText);
            }
        });
    });
});
</script>



{% endblock %}