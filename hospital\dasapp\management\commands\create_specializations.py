from django.core.management.base import BaseCommand
from django.utils import timezone
from dasapp.models import Specialization

class Command(BaseCommand):
    help = 'Creates basic specializations for the hospital'

    def handle(self, *args, **options):
        specializations = [
            "Cardiology",
            "Dermatology",
            "Endocrinology",
            "Gastroenterology",
            "Neurology",
            "Obstetrics and Gynecology",
            "Oncology",
            "Ophthalmology",
            "Orthopedics",
            "Pediatrics",
            "Psychiatry",
            "Pulmonology",
            "Radiology",
            "Urology"
        ]
        
        created_count = 0
        for spec_name in specializations:
            spec, created = Specialization.objects.get_or_create(
                sname=spec_name,
                defaults={
                    'created_at': timezone.now(),
                    'updated_at': timezone.now()
                }
            )
            if created:
                created_count += 1
                self.stdout.write(self.style.SUCCESS(f'Created specialization: {spec_name}'))
            else:
                self.stdout.write(f'Specialization already exists: {spec_name}')
        
        self.stdout.write(self.style.SUCCESS(f'Created {created_count} new specializations'))
