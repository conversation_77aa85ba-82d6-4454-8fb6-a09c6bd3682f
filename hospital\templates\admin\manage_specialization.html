{% extends 'base.html' %}
{% block content %}


<div class="content container-fluid">

    <div class="page-header">
    <div class="row align-items-center">
    <div class="col">
    <h3 class="page-title">Manage Specialization</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Manage Specialization</li>
    </ul>
    </div>
    
    </div>
    </div>
    
    <div class="row">
    <div class="col-sm-12">
    <div class="card card-table">
    <div class="card-body">
        {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'error' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
           {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'success' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
    <div class="table-responsive">
    <table class="table table-hover table-center mb-0 datatable">
    <thead>
    <tr>
    <th>S.No</th>
    <th>Specialization</th>
    <th>Creation Date</th>
    <th class="text-right">Action</th>
    </tr>
    </thead>
    <tbody>
        {% for i in specialization %}
    <tr>
    <td>{{i.id}}</td>
    <td>{{i.sname}}</td>
    <td>{{i.created_at}}</td>
    <td class="text-right">
    <div class="actions">
    <a href="{% url 'update_specilizations' i.id %}" class="btn btn-sm bg-success-light mr-2">
    <i class="fas fa-pen"></i>
    </a>
    <a href="{% url 'delete_specilizations' i.id %}" class="btn btn-sm bg-danger-light">
    <i class="fas fa-trash"></i>
    </a>
    </div>
    </td>
    </tr> {% endfor %}
   
    </tbody>
    </table>
    </div>
    </div>
    </div>
    </div>
    </div>
    </div>


{% endblock %}