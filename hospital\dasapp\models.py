from django.db import models
from django.contrib.auth.models import AbstractUser
from django.utils import timezone

class CustomUser(AbstractUser):
    USER ={
        (1,'admin'),
        (2,'doc'),
        (3,'patient'),
        (4,'kitchen'),
    }
    user_type = models.CharField(choices=USER,max_length=50,default=1)

    profile_pic = models.ImageField(upload_to='media/profile_pic')

class Specialization(models.Model):
    sname = models.CharField(max_length=200)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.sname



class DoctorReg(models.Model):
    admin = models.OneToOneField(CustomUser, on_delete=models.CASCADE, null=True, blank=True)
    fee = models.DecimalField(max_digits=10, decimal_places=2,default=0)
    mobilenumber = models.Char<PERSON>ield(max_length=11)
    specialization_id = models.Foreign<PERSON>ey(Specialization, on_delete=models.CASCADE)
    regdate_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        if self.admin:
            return f"{self.admin.first_name} {self.admin.last_name} - {self.mobilenumber}"
        else:
            return f"User not associated - {self.mobilenumber}"

class PatientReg(models.Model):
    admin = models.OneToOneField(CustomUser, on_delete=models.CASCADE, null=True, blank=True)
    mobilenumber = models.CharField(max_length=11,unique=True)
    gender = models.CharField(max_length=100)
    address = models.TextField()
    regdate_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)


class Appointment(models.Model):
    appointmentnumber = models.IntegerField(default=0)
    spec_id = models.ForeignKey(Specialization, on_delete=models.CASCADE,default=0)
    pat_id = models.ForeignKey(PatientReg, on_delete=models.CASCADE,default=0)
    date_of_appointment = models.CharField(max_length=250)
    time_of_appointment = models.CharField(max_length=250)
    doctor_id = models.ForeignKey(DoctorReg, on_delete=models.CASCADE)
    additional_msg = models.TextField(blank=True)
    remark = models.CharField(max_length=250,default=0)
    status = models.CharField(default=0,max_length=200)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)



class Page(models.Model):
    # Existing fields (keeping them for potential other uses or backward compatibility)
    pagetitle = models.CharField(max_length=250, blank=True, null=True) # General page title if needed elsewhere
    address = models.CharField(max_length=250, blank=True, null=True)
    aboutus = models.TextField(blank=True, null=True) # General about us if needed elsewhere
    email = models.EmailField(max_length=200, blank=True, null=True)
    mobilenumber = models.IntegerField(default=0, blank=True, null=True)

    # New fields for Home Page Content
    home_banner_subtitle = models.CharField(max_length=100, default="Total Health care solution")
    home_banner_title = models.CharField(max_length=100, default="Your most trusted health partner")

    home_feature1_title = models.CharField(max_length=50, default="Online Appoinment")
    home_feature1_text = models.TextField(default="Get ALl time support for emergency.We have introduced the principle of family medicine.")
    # home_feature1_button_text = models.CharField(max_length=30, default="Make a appoinment") # Button text might be static

    home_feature2_title = models.CharField(max_length=50, default="Working Hours")
    home_feature2_hours = models.TextField(default="Sun - Wed : 8:00 - 17:00\nThu - Fri : 9:00 - 17:00\nSat - sun : 10:00 - 17:00", help_text="Enter each day/time on a new line.")

    home_feature3_title = models.CharField(max_length=50, default="1-************") # Emergency number/title
    home_feature3_text = models.TextField(default="Get ALl time support for emergency.We have introduced the principle of family medicine.Get Conneted with us for any urgency .")

    home_service_title = models.CharField(max_length=100, default="Award winning patient care")
    # Individual service titles/text could be added here if needed, or managed differently (e.g., separate Service model)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        # Use a more descriptive name, perhaps indicating it's for website content
        return f"Website Content (ID: {self.id})"

class AddPatient(models.Model):
    doctor_id = models.ForeignKey(DoctorReg, on_delete=models.CASCADE)
    name = models.CharField(max_length=250)
    mobilenumber = models.CharField(max_length=11, unique=True)
    email = models.EmailField(max_length=200)
    gender = models.CharField(max_length=100)
    address = models.TextField()
    age = models.IntegerField()

    medicalhistory = models.TextField()
    regdate_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

class MedicalHistory(models.Model):
    pat_id = models.ForeignKey(AddPatient, on_delete=models.CASCADE, related_name='medical_histories', default=0)
    bloodpressure = models.CharField(max_length=250)
    weight = models.CharField(max_length=250)
    bloodsugar = models.CharField(max_length=250)
    bodytemp = models.CharField(max_length=250)
    prescription = models.TextField()
    visitingdate_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

# Meal tracking models
# Meal status options
MEAL_STATUS_CHOICES = [
    ('preparing', 'Preparing in Kitchen'),
    ('ready', 'Ready for Delivery'),
    ('in_transit', 'In Transit'),
    ('delivered', 'Delivered to Patient'),
    ('consumed', 'Consumed'),
    ('returned', 'Returned to Kitchen'),
    ('disposed', 'Disposed'),
]

# Meal types
MEAL_TYPE_CHOICES = [
    ('breakfast', 'Breakfast'),
    ('lunch', 'Lunch'),
    ('dinner', 'Dinner'),
    ('snack', 'Snack'),
]

# Dietary restrictions
DIETARY_RESTRICTION_CHOICES = [
    ('none', 'No Restrictions'),
    ('vegetarian', 'Vegetarian'),
    ('vegan', 'Vegan'),
    ('gluten_free', 'Gluten Free'),
    ('dairy_free', 'Dairy Free'),
    ('nut_free', 'Nut Free'),
    ('low_sodium', 'Low Sodium'),
    ('diabetic', 'Diabetic'),
    ('low_fat', 'Low Fat'),
    ('kosher', 'Kosher'),
    ('halal', 'Halal'),
]

class MealPlan(models.Model):
    """Model for patient meal plans"""
    patient = models.ForeignKey(AddPatient, on_delete=models.CASCADE, related_name='meal_plans')
    created_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='created_meal_plans')
    updated_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='updated_meal_plans')
    dietary_restrictions = models.CharField(max_length=50, choices=DIETARY_RESTRICTION_CHOICES, default='none')
    special_instructions = models.TextField(blank=True, null=True)
    calories_per_day = models.IntegerField(default=2000)
    protein_per_day = models.IntegerField(default=50)  # in grams
    carbs_per_day = models.IntegerField(default=250)   # in grams
    fat_per_day = models.IntegerField(default=70)      # in grams
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Meal Plan for {self.patient.name}"

class Meal(models.Model):
    """Model for individual meals"""
    meal_plan = models.ForeignKey(MealPlan, on_delete=models.CASCADE, related_name='meals')
    meal_type = models.CharField(max_length=20, choices=MEAL_TYPE_CHOICES)
    name = models.CharField(max_length=255)
    description = models.TextField(blank=True, null=True)
    calories = models.IntegerField()
    protein = models.IntegerField()  # in grams
    carbs = models.IntegerField()    # in grams
    fat = models.IntegerField()      # in grams
    preparation_instructions = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.meal_type})"

class MealDelivery(models.Model):
    """Model for tracking meal deliveries"""
    meal = models.ForeignKey(Meal, on_delete=models.CASCADE, related_name='deliveries')
    patient = models.ForeignKey(AddPatient, on_delete=models.CASCADE, related_name='meal_deliveries')
    prepared_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='prepared_meals')
    delivered_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='delivered_meals')
    collected_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, related_name='collected_meals')
    qr_code = models.CharField(max_length=255, unique=True)
    status = models.CharField(max_length=20, choices=MEAL_STATUS_CHOICES, default='preparing')
    scheduled_time = models.DateTimeField()
    preparation_time = models.DateTimeField(null=True, blank=True)
    delivery_time = models.DateTimeField(null=True, blank=True)
    consumption_time = models.DateTimeField(null=True, blank=True)
    return_time = models.DateTimeField(null=True, blank=True)
    feedback = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.meal.name} for {self.patient.name} - {self.get_status_display()}"

    def update_status(self, new_status, user=None):
        """Update the status and corresponding timestamp"""
        self.status = new_status

        # Update the corresponding timestamp based on the new status
        now = timezone.now()
        if new_status == 'ready':
            self.preparation_time = now
        elif new_status == 'delivered':
            self.delivery_time = now
            self.delivered_by = user
        elif new_status == 'consumed':
            self.consumption_time = now
        elif new_status == 'returned' or new_status == 'disposed':
            self.return_time = now
            self.collected_by = user

        self.save()

class MealStatusLog(models.Model):
    """Model for logging meal status changes"""
    meal_delivery = models.ForeignKey(MealDelivery, on_delete=models.CASCADE, related_name='status_logs')
    previous_status = models.CharField(max_length=20, choices=MEAL_STATUS_CHOICES)
    new_status = models.CharField(max_length=20, choices=MEAL_STATUS_CHOICES)
    changed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.meal_delivery} - {self.previous_status} to {self.new_status}"
