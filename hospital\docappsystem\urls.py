"""
URL configuration for docappsystem project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.0/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path
from .import views,adminviews,docviews,userviews,mealviews,kitchenviews,docmealviews,patientmealviews
from django.conf.urls.static import static
from django.conf import settings
from django.http import HttpResponse

# Simple health check view
def health_check(request):
    return HttpResponse("healthy", content_type="text/plain")

urlpatterns = [
    path('health', health_check, name='health_check'),
    path('admin/', admin.site.urls),
    path('base/', views.BASE, name='base'),
    path('login', views.LOGIN, name='login'),

    path('doLogin', views.doLogin, name='doLogin'),
    path('doLogout', views.doLogout, name='logout'),

    # This is admin panel
    path('Admin/AdminHome', adminviews.ADMINHOME, name='admin_home'),
    path('Admin/Specialization', adminviews.SPECIALIZATION, name='add_specilizations'),
    path('Admin/ManageSpecialization', adminviews.MANAGESPECIALIZATION, name='manage_specilizations'),
    path('Admin/DeleteSpecialization/<str:id>', adminviews.DELETE_SPECIALIZATION, name='delete_specilizations'),
    path('UpdateSpecialization/<str:id>', adminviews.UPDATE_SPECIALIZATION, name='update_specilizations'),
    path('UPDATE_Specialization_DETAILS', adminviews.UPDATE_SPECIALIZATION_DETAILS, name='update_specilizations_details'),
    path('Admin/DoctorList', adminviews.DoctorList, name='viewdoctorlist'),
    path('Admin/ViewDoctorDetails/<str:id>', adminviews.ViewDoctorDetails, name='viewdoctordetails'),
    path('Admin/ViewDoctorAppointmentList/<str:id>', adminviews.ViewDoctorAppointmentList, name='viewdoctorappointmentlist'),
    path('Admin/ViewDOCPatient/<str:id>', adminviews.View_DOCPatient, name='viewdocpatient'),
    path('Admin/ViewCheckPatient/<str:id>', adminviews.ViewCheckAddPatient, name='viewcheckaddpatient'),

    path('Admin/ViewPatientDetails/<str:id>', adminviews.ViewPatientDetails, name='viewpatientdetails'),
    path('SearchDoctor', adminviews.Search_Doctor, name='search_doctor'),
    path('Admin/RegUsers', adminviews.RegUsersDetails, name='regusers'),
    path('Admin/DeleteRegusers/<str:id>', adminviews.DELETE_REGUSERS, name='delusersdetails'),
    path('Admin/RegUserAppointment/<str:id>', adminviews.Reg_User_Appoinments, name='regusersapp'),

    path('DoctorBetweenDateReport', adminviews.Doctor_Between_Date_Report, name='doctor_between_date_report'),

    #Website Page
     path('Website/update', adminviews.WEBSITE_UPDATE, name='website_update'),
     path('UPDATE_WEBSITE_DETAILS', adminviews.UPDATE_WEBSITE_DETAILS, name='update_website_details'),

    # This is Doctor Panel
    path('docsignup/', docviews.DOCSIGNUP, name='docsignup'),
    path('Doctor/DocHome', docviews.DOCTORHOME, name='doctor_home'),
    path('Doctor/AddPatient', docviews.Add_Patient, name='add_patient'),
    path('Doctor/ManagePatient', docviews.Manage_Patient, name='manage_patient'),
    path('Doctor/ViewPatient/<str:id>', docviews.View_Patient, name='viewpatient'),
    path('Doctor/EditPatient', docviews.edit_patient, name='editpatient'),
    path('Doctor/ViewCheckPatient/<str:id>', docviews.ViewCheckPatient, name='viewcheckpatient'),
    path('Doctor/UpdateMedRecPatient', docviews.update_med_rec_patient, name='updatemedrecpatient'),
    path('Doctor/ViewAppointment', docviews.View_Appointment, name='view_appointment'),
    path('DoctorPatientAppointmentDetails/<str:id>', docviews.Patient_Appointment_Details, name='patientappointmentdetails'),
    path('AppointmentDetailsRemark/Update', docviews.Patient_Appointment_Details_Remark, name='patient_appointment_details_remark'),
    path('DoctorPatientApprovedAppointment', docviews.Patient_Approved_Appointment, name='patientapprovedappointment'),
    path('DoctorPatientCancelledAppointment', docviews.Patient_Cancelled_Appointment, name='patientcancelledappointment'),
    path('DoctorPatientNewAppointment', docviews.Patient_New_Appointment, name='patientnewappointment'),
    path('DoctorPatientListApprovedAppointment', docviews.Patient_List_Approved_Appointment, name='patientlistappointment'),
    path('DoctorAppointmentList/<str:id>', docviews.DoctorAppointmentList, name='doctorappointmentlist'),
    path('PatientAppointmentPrescription', docviews.Patient_Appointment_Prescription, name='patientappointmentprescription'),
    path('PatientAppointmentCompleted', docviews.Patient_Appointment_Completed, name='patientappointmentcompleted'),
    path('SearchAppointment', docviews.Search_Appointments, name='search_appointment'),
    path('SearchPatient', docviews.Search_Patient, name='search_patient'),
    path('BetweenDateReport', docviews.Between_Date_Report, name='between_date_report'),
    path('BetweenDatePatientReport', docviews.Between_Date_Patient_Report, name='between_date_patient_report'),

    #This is User Panel
    path('PatientRegsitratios', userviews.PATIENTREGISTRATION, name='patreg'),
    path('Pat/PatHome', userviews.PATIENTHOME, name='userhome'),
    path('userbase/', userviews.USERBASE, name='userbase'),
    path('', userviews.Index, name='index'),
    path('patientappointment/', userviews.create_appointment, name='patientappointment'),

    path('get_doctor/', userviews.get_doctor, name='get_doctor'),
    path('User_SearchAppointment', userviews.User_Search_Appointments, name='user_search_appointment'),
    path('ViewAppointmentHistory', userviews.View_Appointment_History, name='view_appointment_history'),
    path('cancel_appointment/<str:id>', userviews.cancel_appointment, name='cancel_appointment'),
    path('ViewAppointmentDetails/<str:id>/', userviews.View_Appointment_Details, name='viewappointmentdetails'),
    path('Doctor', userviews.Doctor, name='doctor'),
    path('Aboutus', userviews.Aboutus, name='aboutus'),
    path('Contactus', userviews.Contactus, name='contactus'),






    #profile path
    path('Profile', views.PROFILE, name='profile'),
    path('Profile/update', views.PROFILE_UPDATE, name='profile_update'),
    path('Password', views.CHANGE_PASSWORD, name='change_password'),

    # General Meal tracking paths
    path('meals/dashboard/', mealviews.meal_dashboard, name='meal_dashboard'),
    path('meals/tracking/', mealviews.meal_tracking, name='meal_tracking'),
    path('meals/create/', mealviews.create_meal, name='create_meal'),
    path('meals/detail/<int:meal_id>/', mealviews.meal_detail, name='meal_detail'),
    path('meals/delivery/create/', mealviews.create_meal_delivery, name='create_meal_delivery'),
    path('meals/delivery/<int:delivery_id>/', mealviews.meal_delivery_detail, name='meal_delivery_detail'),
    path('meals/delivery/<int:delivery_id>/update/', mealviews.update_meal_status, name='update_meal_status'),
    path('meals/scan/', mealviews.scan_qr_code, name='scan_qr_code'),
    path('meals/delivery/<int:delivery_id>/print/', mealviews.print_qr_code, name='print_qr_code'),
    path('meals/plans/', mealviews.meal_plans, name='meal_plans'),
    path('meals/plans/create/', mealviews.create_meal_plan, name='create_meal_plan'),
    path('meals/plans/<int:plan_id>/', mealviews.meal_plan_detail, name='meal_plan_detail'),

    # Kitchen staff paths
    path('kitchen/', kitchenviews.kitchen_home, name='kitchen_home'),
    path('kitchen/orders/', kitchenviews.kitchen_orders, name='kitchen_orders'),
    path('kitchen/meals/', kitchenviews.kitchen_meals, name='kitchen_meals'),
    path('kitchen/meals/add/', kitchenviews.add_meal, name='add_meal'),
    path('kitchen/meals/<int:meal_id>/', kitchenviews.kitchen_meal_detail, name='kitchen_meal_detail'),
    path('kitchen/meals/<int:meal_id>/edit/', kitchenviews.edit_meal, name='edit_meal'),
    path('kitchen/inventory/', kitchenviews.kitchen_inventory, name='kitchen_inventory'),
    path('kitchen/reports/', kitchenviews.kitchen_reports, name='kitchen_reports'),

    # Doctor meal management paths
    path('doctor/meals/', docmealviews.doctor_meal_dashboard, name='doctor_meal_dashboard'),
    path('doctor/meals/plans/', docmealviews.doctor_patient_meal_plans, name='doctor_patient_meal_plans'),
    path('doctor/meals/plans/create/', docmealviews.doctor_create_meal_plan, name='doctor_create_meal_plan'),
    path('doctor/meals/plans/<int:plan_id>/', docmealviews.doctor_meal_plan_detail, name='doctor_meal_plan_detail'),
    path('doctor/meals/order/', docmealviews.doctor_order_meal, name='doctor_order_meal'),
    path('doctor/meals/orders/', docmealviews.doctor_meal_orders, name='doctor_meal_orders'),
    path('doctor/meals/delivery/<int:delivery_id>/', docmealviews.doctor_meal_delivery_detail, name='doctor_meal_delivery_detail'),

    # Patient meal viewing paths
    path('patient/meals/', patientmealviews.patient_meal_dashboard, name='patient_meal_dashboard'),
    path('patient/meals/plans/<int:plan_id>/', patientmealviews.patient_meal_plan_detail, name='patient_meal_plan_detail'),
    path('patient/meals/detail/<int:meal_id>/', patientmealviews.patient_meal_detail, name='patient_meal_detail'),
    path('patient/meals/delivery/<int:delivery_id>/', patientmealviews.patient_meal_delivery_detail, name='patient_meal_delivery_detail'),
    path('patient/meals/history/', patientmealviews.patient_meal_history, name='patient_meal_history'),
    path('patient/meals/feedback/<int:delivery_id>/', patientmealviews.patient_meal_feedback, name='patient_meal_feedback'),

]+ static(settings.MEDIA_URL, document_root = settings.MEDIA_ROOT)
