{% extends 'base.html' %}
{% block content %}

<div class="content container-fluid">

    {% if user.user_type == '1' %}
    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Change Password</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Change Password</li>
    </ul>
    </div>
    </div>
    </div>
    {% elif user.user_type == '2' %}
    <div class="page-header">
    <div class="row">
    <div class="col">
    <h3 class="page-title">Change Password</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Change Password</li>
    </ul>
    </div>
    </div>
    </div>
    {% else  %}
    <div class="page-header">
        <div class="row">
        <div class="col">
        <h3 class="page-title">Change Password</h3>
        <ul class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'userhome' %}">Dashboard</a></li>
        <li class="breadcrumb-item active">Change Password</li>
        </ul>
        </div>
        </div>
        </div>{% endif %}
    <div class="row">
    <div class="col-lg-12">
    <div class="card">
    <div class="card-header">
    <h5 class="card-title">Change Password</h5>
    </div>
    <div class="card-body">
        {% csrf_token %}
        {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'error' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
           {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'success' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
    <form action="#" method="post">
        {% csrf_token %}
    <div class="form-group row">
    <label class="col-form-label col-md-2">Current Password</label>
    <div class="col-md-10">
        <input id="password" type="password"  class="form-control" name="cpwd"  required="true">
    </div>
    </div>
    <div class="form-group row">
    <label class="col-form-label col-md-2">New Password</label>
    <div class="col-md-10">
        <input id="password" type="password" name="npwd" class="form-control" required="true">
    </div>
    </div>
    
    <div class="form-group row">
        <div class="col-sm-10">
            <button type="submit" class="btn btn-dark">Change</button>
        </div>
    </div>
   
    
    
    </form>
    </div>
    </div>
  
    </div>
    </div>
    </div>



{% endblock %}