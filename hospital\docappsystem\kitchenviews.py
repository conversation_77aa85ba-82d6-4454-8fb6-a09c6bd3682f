from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q, Count
from django.core.paginator import Paginator
from dasapp.models import (
    CustomUser, AddPatient, MealPlan, Meal, MealDelivery, MealStatusLog,
    MEAL_STATUS_CHOICES, MEAL_TYPE_CHOICES, DIETARY_RESTRICTION_CHOICES
)
import qrcode
import io
import uuid
import base64
from datetime import datetime, timedelta

# Kitchen Dashboard
@login_required(login_url='/')
def kitchen_home(request):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    # Get counts for different meal statuses
    preparing_count = MealDelivery.objects.filter(status='preparing').count()
    ready_count = MealDelivery.objects.filter(status='ready').count()
    in_transit_count = MealDelivery.objects.filter(status='in_transit').count()
    delivered_count = MealDelivery.objects.filter(status='delivered').count()
    consumed_count = MealDelivery.objects.filter(status='consumed').count()
    returned_count = MealDelivery.objects.filter(status='returned').count()

    # Get today's meal deliveries
    today = timezone.now().date()
    today_deliveries = MealDelivery.objects.filter(
        scheduled_time__date=today
    ).order_by('scheduled_time')

    # Get meal type distribution
    meal_types = MealDelivery.objects.filter(
        scheduled_time__date=today
    ).values('meal__meal_type').annotate(count=Count('id'))

    # Get upcoming meals for the next 7 days
    next_week = today + timedelta(days=7)
    upcoming_meals = MealDelivery.objects.filter(
        scheduled_time__date__gt=today,
        scheduled_time__date__lte=next_week
    ).order_by('scheduled_time')

    context = {
        'preparing_count': preparing_count,
        'ready_count': ready_count,
        'in_transit_count': in_transit_count,
        'delivered_count': delivered_count,
        'consumed_count': consumed_count,
        'returned_count': returned_count,
        'today_deliveries': today_deliveries,
        'meal_types': meal_types,
        'upcoming_meals': upcoming_meals,
    }

    return render(request, 'kitchen/dashboard.html', context)

# Kitchen Orders
@login_required(login_url='/')
def kitchen_orders(request):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    # Get filter parameters
    status_filter = request.GET.get('status', '')
    date_filter = request.GET.get('date', '')
    meal_type_filter = request.GET.get('meal_type', '')

    # Start with all meal deliveries
    orders = MealDelivery.objects.all()

    # Apply filters
    if status_filter:
        orders = orders.filter(status=status_filter)

    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            orders = orders.filter(scheduled_time__date=filter_date)
        except ValueError:
            pass

    if meal_type_filter:
        orders = orders.filter(meal__meal_type=meal_type_filter)

    # Order by scheduled time
    orders = orders.order_by('scheduled_time')

    # Pagination
    paginator = Paginator(orders, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
        'status_choices': MEAL_STATUS_CHOICES,
        'meal_type_choices': MEAL_TYPE_CHOICES,
        'status_filter': status_filter,
        'date_filter': date_filter,
        'meal_type_filter': meal_type_filter,
    }

    return render(request, 'kitchen/orders.html', context)

# Kitchen Meals Management
@login_required(login_url='/')
def kitchen_meals(request):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    # Get all meals
    meals = Meal.objects.all().order_by('meal_type', 'name')

    context = {
        'meals': meals,
        'meal_type_choices': MEAL_TYPE_CHOICES,
    }

    return render(request, 'kitchen/meals.html', context)

# Add New Meal
@login_required(login_url='/')
def add_meal(request):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    if request.method == 'POST':
        # Get form data
        meal_plan_id = request.POST.get('meal_plan')
        meal_type = request.POST.get('meal_type')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        calories = request.POST.get('calories')
        protein = request.POST.get('protein')
        carbs = request.POST.get('carbs')
        fat = request.POST.get('fat')
        preparation_instructions = request.POST.get('preparation_instructions', '')

        # Validate required fields
        if not all([meal_plan_id, meal_type, name, calories, protein, carbs, fat]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('add_meal')

        try:
            # Get the meal plan
            meal_plan = MealPlan.objects.get(id=meal_plan_id)

            # Create the meal
            meal = Meal.objects.create(
                meal_plan=meal_plan,
                meal_type=meal_type,
                name=name,
                description=description,
                calories=int(calories),
                protein=int(protein),
                carbs=int(carbs),
                fat=int(fat),
                preparation_instructions=preparation_instructions
            )

            messages.success(request, f'Meal "{name}" created successfully')
            return redirect('kitchen_meal_detail', meal_id=meal.id)

        except Exception as e:
            messages.error(request, f'Error creating meal: {str(e)}')
            return redirect('add_meal')

    # GET request - show the form
    meal_plans = MealPlan.objects.filter(is_active=True)

    context = {
        'meal_plans': meal_plans,
        'meal_type_choices': MEAL_TYPE_CHOICES,
    }

    return render(request, 'kitchen/add_meal.html', context)

# Kitchen Meal Detail
@login_required(login_url='/')
def kitchen_meal_detail(request, meal_id):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    meal = get_object_or_404(Meal, id=meal_id)

    # Get recent deliveries of this meal
    recent_deliveries = MealDelivery.objects.filter(meal=meal).order_by('-scheduled_time')[:10]

    context = {
        'meal': meal,
        'recent_deliveries': recent_deliveries,
    }

    return render(request, 'kitchen/meal_detail.html', context)

# Edit Meal
@login_required(login_url='/')
def edit_meal(request, meal_id):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    meal = get_object_or_404(Meal, id=meal_id)

    if request.method == 'POST':
        # Get form data
        meal_type = request.POST.get('meal_type')
        name = request.POST.get('name')
        description = request.POST.get('description', '')
        calories = request.POST.get('calories')
        protein = request.POST.get('protein')
        carbs = request.POST.get('carbs')
        fat = request.POST.get('fat')
        preparation_instructions = request.POST.get('preparation_instructions', '')

        # Validate required fields
        if not all([meal_type, name, calories, protein, carbs, fat]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('edit_meal', meal_id=meal_id)

        try:
            # Update the meal
            meal.meal_type = meal_type
            meal.name = name
            meal.description = description
            meal.calories = int(calories)
            meal.protein = int(protein)
            meal.carbs = int(carbs)
            meal.fat = int(fat)
            meal.preparation_instructions = preparation_instructions
            meal.save()

            messages.success(request, f'Meal "{name}" updated successfully')
            return redirect('kitchen_meal_detail', meal_id=meal.id)

        except Exception as e:
            messages.error(request, f'Error updating meal: {str(e)}')
            return redirect('edit_meal', meal_id=meal_id)

    # GET request - show the form
    context = {
        'meal': meal,
        'meal_type_choices': MEAL_TYPE_CHOICES,
    }

    return render(request, 'kitchen/edit_meal.html', context)

# Kitchen Inventory
@login_required(login_url='/')
def kitchen_inventory(request):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    # This is a placeholder for future inventory management
    context = {
        'inventory_items': [],
    }

    return render(request, 'kitchen/inventory.html', context)

# Kitchen Reports
@login_required(login_url='/')
def kitchen_reports(request):
    # Check if user is kitchen staff or admin
    if not (request.user.user_type == '4' or request.user.user_type == '1'):
        messages.error(request, "You don't have permission to access this page")
        return redirect('login')

    # Get date range parameters
    start_date_str = request.GET.get('start_date', '')
    end_date_str = request.GET.get('end_date', '')

    # Default to last 30 days if no dates provided
    today = timezone.now().date()
    if not start_date_str:
        start_date = today - timedelta(days=30)
    else:
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError:
            start_date = today - timedelta(days=30)

    if not end_date_str:
        end_date = today
    else:
        try:
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            end_date = today

    # Get meal deliveries in date range
    deliveries = MealDelivery.objects.filter(
        scheduled_time__date__gte=start_date,
        scheduled_time__date__lte=end_date
    )

    # Calculate statistics
    total_meals = deliveries.count()
    meals_by_type = deliveries.values('meal__meal_type').annotate(count=Count('id'))
    meals_by_status = deliveries.values('status').annotate(count=Count('id'))

    # Calculate daily meal counts
    daily_counts = []
    current_date = start_date
    while current_date <= end_date:
        count = deliveries.filter(scheduled_time__date=current_date).count()
        daily_counts.append({
            'date': current_date.strftime('%Y-%m-%d'),
            'count': count
        })
        current_date += timedelta(days=1)

    context = {
        'start_date': start_date,
        'end_date': end_date,
        'total_meals': total_meals,
        'meals_by_type': meals_by_type,
        'meals_by_status': meals_by_status,
        'daily_counts': daily_counts,
        'meal_type_choices': dict(MEAL_TYPE_CHOICES),
        'status_choices': dict(MEAL_STATUS_CHOICES),
    }

    return render(request, 'kitchen/reports.html', context)
