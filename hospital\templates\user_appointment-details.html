{% extends 'userbase.html' %}
{% load static %}

{% block content %}
<div class="container-fluid page-header py-5 mb-5 wow fadeIn" data-wow-delay="0.1s">
    <div class="container py-5">
        <h1 class="display-3 text-white mb-3 animated slideInDown">Appointment</h1>
        <nav aria-label="breadcrumb animated slideInDown">
            <ol class="breadcrumb text-uppercase mb-0">
                <li class="breadcrumb-item"><a class="text-white" href="#">Home</a></li>
                <li class="breadcrumb-item"><a class="text-white" href="#">Pages</a></li>
                <li class="breadcrumb-item text-primary active" aria-current="page">Appointment</li>
            </ol>
        </nav>
    </div>
</div>
<!-- Page Header End -->



<!-- Appointment Start -->
<div class="container-xxl py-10">
    <div class="container">
        <div class="row">
            <div class="col-sm-12">
             
            
                                
            <div class="card card-table">
        
            <div class="card-body">
                {% if messages %}
                {% for message in messages %}
                 {% if message.tags == 'error' %}
               <div class="alert alert-warning alert-dismissible fade show" role="alert">
              {{message}}
             <button type="button" class="close" data-dismiss="alert" aria-label="Close">
             <span aria-hidden="true">&times;</span>
                 </button>
                  </div>
               {% endif %}
                {% endfor %}
               {% endif %}
                   {% if messages %}
                {% for message in messages %}
                 {% if message.tags == 'success' %}
               <div class="alert alert-warning alert-dismissible fade show" role="alert">
              {{message}}
             <button type="button" class="close" data-dismiss="alert" aria-label="Close">
             <span aria-hidden="true">&times;</span>
                 </button>
                  </div>
               {% endif %}
                {% endfor %}
               {% endif %}
            <div class="table-responsive">
                {% for i in patientdetails %}
                <h6>Below is the details of Appointment Number: {{i.appointmentnumber}}  <strong style="padding-left: 600px;">Date: {{i.date_of_appointment}}</strong></h6> 
                <br>
                <table class="table table-hover table-center mb-0 datatable">
                  
                   
                    <tr></tr>
                      
                    <tr>
                          
                          <th>Patient Name</th>
                          <td>{{i.fullname}}</td>
                          <th>Patient Contact Number</th>
                          <td>{{i.mobilenumber}}</td>
                        </tr>
                        <tr>
                          
                          <th>Patient Email</th>
                          <td>{{i.email}}</td>
                          <th>Date of Appointment</th>
                          <td>{{i.date_of_appointment}}</td>
                        </tr>
                        <tr>
                          
                          <th>Date of Time</th>
                          <td>{{i.time_of_appointment}}</td>
                          <th>Message</th>
                          <td>{{i.additional_msg}}</td>
                        </tr>
                        <tr>
                                            
                          {% if i.status == '0' %}
                          <th>Doctor Remark</th>
                          <td>Not Updatet Yet</td>
                          {% else %}
                          <tr>
                          <th>Doctor Remark</th>
                          <td>{{ i.remark}}</td>{% endif %}
                          
                            {% if i.status == '0' %}
                            <th>Staus</th>
                            <td>Not Updatet Yet</td>
                            {% else %}
                            <th>Status</th>
                            <td>{{ i.status}}</td> {% endif %}
                            </tr>
              
                            <tr>
                                {% if i.status == 'Completed' %}
                                <th>Prescribed Medicine</th>
                                <td>{{ i.prescription}}</td>
                                {% else %}
                                <th>Prescribed Medicine</th>
                                <td>Not Prescribed Yet</td> {% endif %}
                                </tr>
                                <tr>
                                  {% if i.status == 'Completed' %}
                                  <th>Recommended Test</th>
                                  <td>{{ i.recommendedtest}}</td>
                                  {% else %}
                                  <th>Recommended Test</th>
                                  <td>Not Recommended Yet</td> {% endif %}
                                  </tr>
                        
                 
                  </table>
                  {% endfor %}
        
            </div>
            </div>
            </div>
            </div>
            </div>
    </div>
</div>
<!-- Appointment End -->

{% endblock %}

