from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q
from django.core.paginator import Paginator
from dasapp.models import (
    CustomUser, AddPatient, MealPlan, Meal, MealDelivery, MealStatusLog,
    MEAL_STATUS_CHOICES, MEAL_TYPE_CHOICES, DIETARY_RESTRICTION_CHOICES
)
import uuid
from datetime import datetime, timedelta

# Doctor's meal management dashboard
@login_required(login_url='/')
def doctor_meal_dashboard(request):
    # Check if user is a doctor
    if not request.user.user_type == '2':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get doctor's patients
    doctor_id = request.user.id
    patients = AddPatient.objects.filter(doctor_id=doctor_id)
    patient_ids = [patient.id for patient in patients]
    
    # Get meal plans for doctor's patients
    meal_plans = MealPlan.objects.filter(patient_id__in=patient_ids, is_active=True)
    
    # Get recent meal deliveries for doctor's patients
    recent_deliveries = MealDelivery.objects.filter(
        patient_id__in=patient_ids
    ).order_by('-scheduled_time')[:10]
    
    # Get today's meal deliveries
    today = timezone.now().date()
    today_deliveries = MealDelivery.objects.filter(
        patient_id__in=patient_ids,
        scheduled_time__date=today
    ).order_by('scheduled_time')
    
    context = {
        'patients': patients,
        'meal_plans': meal_plans,
        'recent_deliveries': recent_deliveries,
        'today_deliveries': today_deliveries,
    }
    
    return render(request, 'doctor/meal_dashboard.html', context)

# Doctor's patient meal plans
@login_required(login_url='/')
def doctor_patient_meal_plans(request):
    # Check if user is a doctor
    if not request.user.user_type == '2':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get doctor's patients
    doctor_id = request.user.id
    patients = AddPatient.objects.filter(doctor_id=doctor_id)
    patient_ids = [patient.id for patient in patients]
    
    # Get meal plans for doctor's patients
    meal_plans = MealPlan.objects.filter(patient_id__in=patient_ids)
    
    # Filter by patient if specified
    patient_filter = request.GET.get('patient', '')
    if patient_filter:
        try:
            patient_id = int(patient_filter)
            meal_plans = meal_plans.filter(patient_id=patient_id)
        except ValueError:
            pass
    
    context = {
        'meal_plans': meal_plans,
        'patients': patients,
        'patient_filter': patient_filter,
    }
    
    return render(request, 'doctor/patient_meal_plans.html', context)

# Doctor creating a meal plan for a patient
@login_required(login_url='/')
def doctor_create_meal_plan(request):
    # Check if user is a doctor
    if not request.user.user_type == '2':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    if request.method == 'POST':
        # Get form data
        patient_id = request.POST.get('patient')
        dietary_restrictions = request.POST.get('dietary_restrictions')
        special_instructions = request.POST.get('special_instructions', '')
        calories_per_day = request.POST.get('calories_per_day')
        protein_per_day = request.POST.get('protein_per_day')
        carbs_per_day = request.POST.get('carbs_per_day')
        fat_per_day = request.POST.get('fat_per_day')
        
        # Validate required fields
        if not all([patient_id, dietary_restrictions, calories_per_day, protein_per_day, carbs_per_day, fat_per_day]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('doctor_create_meal_plan')
        
        try:
            # Verify patient belongs to this doctor
            doctor_id = request.user.id
            patient = get_object_or_404(AddPatient, id=patient_id, doctor_id=doctor_id)
            
            # Create the meal plan
            meal_plan = MealPlan.objects.create(
                patient=patient,
                created_by=request.user,
                updated_by=request.user,
                dietary_restrictions=dietary_restrictions,
                special_instructions=special_instructions,
                calories_per_day=int(calories_per_day),
                protein_per_day=int(protein_per_day),
                carbs_per_day=int(carbs_per_day),
                fat_per_day=int(fat_per_day),
                is_active=True
            )
            
            messages.success(request, f'Meal plan created successfully for {patient.name}')
            return redirect('doctor_meal_plan_detail', plan_id=meal_plan.id)
        
        except Exception as e:
            messages.error(request, f'Error creating meal plan: {str(e)}')
            return redirect('doctor_create_meal_plan')
    
    # GET request - show the form
    doctor_id = request.user.id
    patients = AddPatient.objects.filter(doctor_id=doctor_id)
    
    context = {
        'patients': patients,
        'dietary_restrictions': DIETARY_RESTRICTION_CHOICES,
    }
    
    return render(request, 'doctor/create_meal_plan.html', context)

# Doctor viewing meal plan details
@login_required(login_url='/')
def doctor_meal_plan_detail(request, plan_id):
    # Check if user is a doctor
    if not request.user.user_type == '2':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get the meal plan and verify it belongs to one of the doctor's patients
    doctor_id = request.user.id
    meal_plan = get_object_or_404(MealPlan, id=plan_id)
    
    # Check if the patient belongs to this doctor
    if not AddPatient.objects.filter(id=meal_plan.patient.id, doctor_id=doctor_id).exists():
        messages.error(request, "You don't have permission to view this meal plan")
        return redirect('doctor_meal_dashboard')
    
    # Get meals for this plan
    meals = Meal.objects.filter(meal_plan=meal_plan).order_by('meal_type')
    
    # Get recent deliveries for this plan
    recent_deliveries = MealDelivery.objects.filter(
        meal__meal_plan=meal_plan
    ).order_by('-scheduled_time')[:10]
    
    context = {
        'meal_plan': meal_plan,
        'meals': meals,
        'recent_deliveries': recent_deliveries,
    }
    
    return render(request, 'doctor/meal_plan_detail.html', context)

# Doctor ordering a meal for a patient
@login_required(login_url='/')
def doctor_order_meal(request):
    # Check if user is a doctor
    if not request.user.user_type == '2':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    if request.method == 'POST':
        # Get form data
        patient_id = request.POST.get('patient')
        meal_id = request.POST.get('meal')
        scheduled_time_str = request.POST.get('scheduled_time')
        notes = request.POST.get('notes', '')
        
        # Validate required fields
        if not all([patient_id, meal_id, scheduled_time_str]):
            messages.error(request, 'Please fill in all required fields')
            return redirect('doctor_order_meal')
        
        try:
            # Verify patient belongs to this doctor
            doctor_id = request.user.id
            patient = get_object_or_404(AddPatient, id=patient_id, doctor_id=doctor_id)
            
            # Get the meal
            meal = get_object_or_404(Meal, id=meal_id)
            
            # Parse scheduled time
            scheduled_time = datetime.strptime(scheduled_time_str, '%Y-%m-%dT%H:%M')
            
            # Generate a unique QR code
            qr_code_data = str(uuid.uuid4())
            
            # Create the meal delivery
            meal_delivery = MealDelivery.objects.create(
                meal=meal,
                patient=patient,
                prepared_by=request.user,
                qr_code=qr_code_data,
                status='preparing',
                scheduled_time=scheduled_time,
                notes=notes
            )
            
            # Create initial status log
            MealStatusLog.objects.create(
                meal_delivery=meal_delivery,
                previous_status='',
                new_status='preparing',
                changed_by=request.user,
                notes='Ordered by doctor'
            )
            
            messages.success(request, f'Meal ordered successfully for {patient.name}')
            return redirect('doctor_meal_delivery_detail', delivery_id=meal_delivery.id)
        
        except Exception as e:
            messages.error(request, f'Error ordering meal: {str(e)}')
            return redirect('doctor_order_meal')
    
    # GET request - show the form
    doctor_id = request.user.id
    patients = AddPatient.objects.filter(doctor_id=doctor_id)
    
    # Get all available meals
    meals = Meal.objects.all().order_by('meal_type', 'name')
    
    # If patient_id is provided, filter meals by their meal plan
    patient_id = request.GET.get('patient', '')
    if patient_id:
        try:
            patient = get_object_or_404(AddPatient, id=patient_id, doctor_id=doctor_id)
            meal_plans = MealPlan.objects.filter(patient=patient, is_active=True)
            if meal_plans.exists():
                meal_plan_ids = [plan.id for plan in meal_plans]
                meals = meals.filter(meal_plan_id__in=meal_plan_ids)
        except (ValueError, AddPatient.DoesNotExist):
            pass
    
    context = {
        'patients': patients,
        'meals': meals,
        'selected_patient': patient_id,
    }
    
    return render(request, 'doctor/order_meal.html', context)

# Doctor viewing meal delivery details
@login_required(login_url='/')
def doctor_meal_delivery_detail(request, delivery_id):
    # Check if user is a doctor
    if not request.user.user_type == '2':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get the meal delivery
    meal_delivery = get_object_or_404(MealDelivery, id=delivery_id)
    
    # Check if the patient belongs to this doctor
    doctor_id = request.user.id
    if not AddPatient.objects.filter(id=meal_delivery.patient.id, doctor_id=doctor_id).exists():
        messages.error(request, "You don't have permission to view this meal delivery")
        return redirect('doctor_meal_dashboard')
    
    # Get status logs
    status_logs = MealStatusLog.objects.filter(meal_delivery=meal_delivery).order_by('-created_at')
    
    context = {
        'delivery': meal_delivery,
        'status_logs': status_logs,
        'status_choices': MEAL_STATUS_CHOICES,
    }
    
    return render(request, 'doctor/meal_delivery_detail.html', context)

# Doctor viewing all meal orders
@login_required(login_url='/')
def doctor_meal_orders(request):
    # Check if user is a doctor
    if not request.user.user_type == '2':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get doctor's patients
    doctor_id = request.user.id
    patients = AddPatient.objects.filter(doctor_id=doctor_id)
    patient_ids = [patient.id for patient in patients]
    
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    date_filter = request.GET.get('date', '')
    patient_filter = request.GET.get('patient', '')
    
    # Start with all meal deliveries for doctor's patients
    meal_deliveries = MealDelivery.objects.filter(patient_id__in=patient_ids)
    
    # Apply filters
    if status_filter:
        meal_deliveries = meal_deliveries.filter(status=status_filter)
    
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            meal_deliveries = meal_deliveries.filter(scheduled_time__date=filter_date)
        except ValueError:
            pass
    
    if patient_filter:
        try:
            patient_id = int(patient_filter)
            meal_deliveries = meal_deliveries.filter(patient_id=patient_id)
        except ValueError:
            pass
    
    # Order by scheduled time
    meal_deliveries = meal_deliveries.order_by('-scheduled_time')
    
    # Pagination
    paginator = Paginator(meal_deliveries, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'patients': patients,
        'status_choices': MEAL_STATUS_CHOICES,
        'status_filter': status_filter,
        'date_filter': date_filter,
        'patient_filter': patient_filter,
    }
    
    return render(request, 'doctor/meal_orders.html', context)
