{% extends 'base.html' %}
{% load static %}
{% load meal_filters %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Manage Meals</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'kitchen_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Manage Meals</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 col-sm-6 mb-3">
                            <a href="{% url 'add_meal' %}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle mr-2"></i> Add New Meal
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <a href="{% url 'kitchen_inventory' %}" class="btn btn-info btn-block">
                                <i class="fas fa-boxes mr-2"></i> Check Inventory
                            </a>
                        </div>
                        <div class="col-md-4 col-sm-6 mb-3">
                            <a href="{% url 'kitchen_orders' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-clipboard-list mr-2"></i> View Orders
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Meals List -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">All Meals</h5>
                </div>
                <div class="card-body">
                    {% if meals %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0 datatable">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Calories</th>
                                    <th>Protein</th>
                                    <th>Carbs</th>
                                    <th>Fat</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for meal in meals %}
                                <tr>
                                    <td>{{ meal.id }}</td>
                                    <td>
                                        <h2 class="table-avatar">
                                            <a href="{% url 'kitchen_meal_detail' meal.id %}">{{ meal.name }}</a>
                                        </h2>
                                    </td>
                                    <td>{{ meal.get_meal_type_display }}</td>
                                    <td>{{ meal.calories }} kcal</td>
                                    <td>{{ meal.protein }} g</td>
                                    <td>{{ meal.carbs }} g</td>
                                    <td>{{ meal.fat }} g</td>
                                    <td>
                                        <div class="actions">
                                            <a href="{% url 'kitchen_meal_detail' meal.id %}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{% url 'edit_meal' meal.id %}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meals found. <a href="{% url 'add_meal' %}">Add a new meal</a> to get started.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Meal Types -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Meal Types</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        {% for type_code, type_name in meal_type_choices %}
                        <div class="col-md-3 col-sm-6 mb-4">
                            <div class="card bg-light">
                                <div class="card-body text-center">
                                    <h5 class="card-title">{{ type_name }}</h5>
                                    <p class="card-text">{{ meals|filter_by_meal_type:type_code|length }} meals</p>
                                    <a href="{% url 'add_meal' %}?meal_type={{ type_code }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus-circle"></i> Add {{ type_name }}
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .actions {
        display: flex;
        gap: 5px;
    }
</style>
{% endblock %}
