from django.shortcuts import render, redirect, get_object_or_404
from django.http import JsonResponse, HttpResponse
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Q
from django.core.paginator import Paginator
from dasapp.models import (
    CustomUser, AddPatient, MealPlan, Meal, MealDelivery, MealStatusLog,
    MEAL_STATUS_CHOICES, MEAL_TYPE_CHOICES, DIETARY_RESTRICTION_CHOICES
)
from datetime import datetime, timedelta

# Patient meal dashboard
@login_required(login_url='/')
def patient_meal_dashboard(request):
    # Check if user is a patient
    if not request.user.user_type == '3':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get patient's information
    patient_user_id = request.user.id
    try:
        patient = AddPatient.objects.get(user_id=patient_user_id)
    except AddPatient.DoesNotExist:
        messages.error(request, "Patient profile not found")
        return redirect('home')
    
    # Get patient's meal plans
    meal_plans = MealPlan.objects.filter(patient=patient, is_active=True)
    
    # Get today's meals
    today = timezone.now().date()
    today_meals = MealDelivery.objects.filter(
        patient=patient,
        scheduled_time__date=today
    ).order_by('scheduled_time')
    
    # Get upcoming meals
    upcoming_meals = MealDelivery.objects.filter(
        patient=patient,
        scheduled_time__date__gt=today,
        scheduled_time__date__lte=today + timedelta(days=7)
    ).order_by('scheduled_time')
    
    # Get recent meal history
    meal_history = MealDelivery.objects.filter(
        patient=patient,
        scheduled_time__date__lt=today
    ).order_by('-scheduled_time')[:10]
    
    context = {
        'patient': patient,
        'meal_plans': meal_plans,
        'today_meals': today_meals,
        'upcoming_meals': upcoming_meals,
        'meal_history': meal_history,
    }
    
    return render(request, 'patient/meal_dashboard.html', context)

# Patient viewing meal plan details
@login_required(login_url='/')
def patient_meal_plan_detail(request, plan_id):
    # Check if user is a patient
    if not request.user.user_type == '3':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get patient's information
    patient_user_id = request.user.id
    try:
        patient = AddPatient.objects.get(user_id=patient_user_id)
    except AddPatient.DoesNotExist:
        messages.error(request, "Patient profile not found")
        return redirect('home')
    
    # Get the meal plan and verify it belongs to this patient
    meal_plan = get_object_or_404(MealPlan, id=plan_id, patient=patient)
    
    # Get meals for this plan
    meals = Meal.objects.filter(meal_plan=meal_plan).order_by('meal_type')
    
    # Get upcoming deliveries for this plan
    today = timezone.now().date()
    upcoming_deliveries = MealDelivery.objects.filter(
        meal__meal_plan=meal_plan,
        scheduled_time__date__gte=today
    ).order_by('scheduled_time')
    
    context = {
        'patient': patient,
        'meal_plan': meal_plan,
        'meals': meals,
        'upcoming_deliveries': upcoming_deliveries,
    }
    
    return render(request, 'patient/meal_plan_detail.html', context)

# Patient viewing meal details
@login_required(login_url='/')
def patient_meal_detail(request, meal_id):
    # Check if user is a patient
    if not request.user.user_type == '3':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get patient's information
    patient_user_id = request.user.id
    try:
        patient = AddPatient.objects.get(user_id=patient_user_id)
    except AddPatient.DoesNotExist:
        messages.error(request, "Patient profile not found")
        return redirect('home')
    
    # Get the meal
    meal = get_object_or_404(Meal, id=meal_id)
    
    # Verify the meal belongs to one of the patient's meal plans
    patient_meal_plans = MealPlan.objects.filter(patient=patient)
    if not meal.meal_plan in patient_meal_plans:
        messages.error(request, "You don't have permission to view this meal")
        return redirect('patient_meal_dashboard')
    
    # Get upcoming deliveries of this meal for this patient
    today = timezone.now().date()
    upcoming_deliveries = MealDelivery.objects.filter(
        meal=meal,
        patient=patient,
        scheduled_time__date__gte=today
    ).order_by('scheduled_time')
    
    context = {
        'patient': patient,
        'meal': meal,
        'upcoming_deliveries': upcoming_deliveries,
    }
    
    return render(request, 'patient/meal_detail.html', context)

# Patient viewing meal delivery details
@login_required(login_url='/')
def patient_meal_delivery_detail(request, delivery_id):
    # Check if user is a patient
    if not request.user.user_type == '3':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get patient's information
    patient_user_id = request.user.id
    try:
        patient = AddPatient.objects.get(user_id=patient_user_id)
    except AddPatient.DoesNotExist:
        messages.error(request, "Patient profile not found")
        return redirect('home')
    
    # Get the meal delivery and verify it belongs to this patient
    meal_delivery = get_object_or_404(MealDelivery, id=delivery_id, patient=patient)
    
    # Get status logs
    status_logs = MealStatusLog.objects.filter(meal_delivery=meal_delivery).order_by('-created_at')
    
    context = {
        'patient': patient,
        'delivery': meal_delivery,
        'status_logs': status_logs,
    }
    
    return render(request, 'patient/meal_delivery_detail.html', context)

# Patient viewing all meal history
@login_required(login_url='/')
def patient_meal_history(request):
    # Check if user is a patient
    if not request.user.user_type == '3':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get patient's information
    patient_user_id = request.user.id
    try:
        patient = AddPatient.objects.get(user_id=patient_user_id)
    except AddPatient.DoesNotExist:
        messages.error(request, "Patient profile not found")
        return redirect('home')
    
    # Get filter parameters
    status_filter = request.GET.get('status', '')
    date_filter = request.GET.get('date', '')
    meal_type_filter = request.GET.get('meal_type', '')
    
    # Start with all meal deliveries for this patient
    meal_deliveries = MealDelivery.objects.filter(patient=patient)
    
    # Apply filters
    if status_filter:
        meal_deliveries = meal_deliveries.filter(status=status_filter)
    
    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            meal_deliveries = meal_deliveries.filter(scheduled_time__date=filter_date)
        except ValueError:
            pass
    
    if meal_type_filter:
        meal_deliveries = meal_deliveries.filter(meal__meal_type=meal_type_filter)
    
    # Order by scheduled time
    meal_deliveries = meal_deliveries.order_by('-scheduled_time')
    
    # Pagination
    paginator = Paginator(meal_deliveries, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'patient': patient,
        'page_obj': page_obj,
        'status_choices': MEAL_STATUS_CHOICES,
        'meal_type_choices': MEAL_TYPE_CHOICES,
        'status_filter': status_filter,
        'date_filter': date_filter,
        'meal_type_filter': meal_type_filter,
    }
    
    return render(request, 'patient/meal_history.html', context)

# Patient providing feedback on a meal
@login_required(login_url='/')
def patient_meal_feedback(request, delivery_id):
    # Check if user is a patient
    if not request.user.user_type == '3':
        messages.error(request, "You don't have permission to access this page")
        return redirect('home')
    
    # Get patient's information
    patient_user_id = request.user.id
    try:
        patient = AddPatient.objects.get(user_id=patient_user_id)
    except AddPatient.DoesNotExist:
        messages.error(request, "Patient profile not found")
        return redirect('home')
    
    # Get the meal delivery and verify it belongs to this patient
    meal_delivery = get_object_or_404(MealDelivery, id=delivery_id, patient=patient)
    
    if request.method == 'POST':
        # Get form data
        feedback = request.POST.get('feedback', '')
        consumed = request.POST.get('consumed', 'no')
        
        # Update the meal delivery
        meal_delivery.feedback = feedback
        
        # Update status if meal was consumed
        if consumed == 'yes' and meal_delivery.status == 'delivered':
            previous_status = meal_delivery.status
            meal_delivery.status = 'consumed'
            meal_delivery.consumption_time = timezone.now()
            
            # Create status log
            MealStatusLog.objects.create(
                meal_delivery=meal_delivery,
                previous_status=previous_status,
                new_status='consumed',
                changed_by=request.user,
                notes='Marked as consumed by patient'
            )
        
        meal_delivery.save()
        
        messages.success(request, 'Thank you for your feedback!')
        return redirect('patient_meal_delivery_detail', delivery_id=meal_delivery.id)
    
    context = {
        'patient': patient,
        'delivery': meal_delivery,
    }
    
    return render(request, 'patient/meal_feedback.html', context)
