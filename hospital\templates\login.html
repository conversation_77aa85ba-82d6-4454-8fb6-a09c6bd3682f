<!DOCTYPE html>
<html lang="en">
{% load static %}
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HMS - Login</title>

    <!-- Favicon -->
    <link rel="shortcut icon" href="{% static 'assets/img/favicon.png'%}">

    <!-- Google Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="{% static 'assets/plugins/bootstrap/css/bootstrap.min.css'%}">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="{% static 'assets/plugins/fontawesome/css/fontawesome.min.css'%}">
    <link rel="stylesheet" href="{% static 'assets/plugins/fontawesome/css/all.min.css'%}">

    <!-- Icofont -->
    <link rel="stylesheet" href="{% static 'assets1/plugins/icofont/icofont.min.css'%}">

    <style>
        :root {
            --primary-color: #4361ee;
            --primary-light: #eaefff;
            --secondary-color: #3f37c9;
            --success-color: #4cc9f0;
            --info-color: #4895ef;
            --warning-color: #f72585;
            --danger-color: #e63946;
            --dark-color: #1d3557;
            --light-color: #f8f9fa;
            --gray-color: #6c757d;
            --gray-light: #e9ecef;
            --gray-dark: #343a40;
            --body-bg: #f5f7fb;
            --sidebar-bg: #1d3557;
            --sidebar-text: #ffffff;
            --card-bg: #ffffff;
            --border-color: #e9ecef;
            --text-color: #333;
            --text-muted: #6c757d;
            --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            --shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
            --shadow-lg: 0 1rem 3rem rgba(0, 0, 0, 0.1);
            --radius-sm: 0.25rem;
            --radius: 0.5rem;
            --radius-lg: 1rem;
            --radius-xl: 1.5rem;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background-color: var(--body-bg);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            color: var(--text-color);
        }

        .page-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: 300px;
            background-color: var(--sidebar-bg);
            color: var(--sidebar-text);
            padding: 20px 0;
            box-shadow: var(--shadow);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('{% static "assets/img/pattern.png" %}');
            opacity: 0.05;
            z-index: 0;
        }

        .main-content {
            flex: 1;
            padding: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            background: linear-gradient(135deg, var(--body-bg) 0%, var(--light-color) 100%);
        }

        .navbar-brand {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 15px 20px;
            margin-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .brand-logo {
            display: flex;
            align-items: center;
        }

        .brand-logo img {
            margin-right: 10px;
            filter: brightness(0) invert(1);
        }

        .navbar-brand h2 {
            color: white;
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            letter-spacing: 1px;
        }

        .navbar-nav {
            padding: 0;
            list-style: none;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 5px;
        }

        .nav-link {
            display: block;
            padding: 12px 20px;
            color: rgba(255, 255, 255, 0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            font-weight: 500;
            font-size: 0.95rem;
            width: 100%;
            text-align: left;
            background: none;
            border: none;
            cursor: pointer;
        }

        .nav-link:hover, .nav-item.active .nav-link {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
            border-left-color: var(--primary-color);
        }

        .nav-link i {
            margin-right: 8px;
            width: 20px;
            text-align: center;
        }

        .btn-link {
            box-shadow: none !important;
            outline: none !important;
        }

        .kitchen-link {
            color: #ffc107;
            font-weight: 600;
            background-color: rgba(255, 193, 7, 0.05);
        }

        .kitchen-link:hover {
            color: #ffdb58;
            background-color: rgba(255, 193, 7, 0.15);
            border-left-color: #ffc107;
        }

        .login-card {
            width: 100%;
            max-width: 450px;
            background: var(--card-bg);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            overflow: hidden;
            transition: var(--transition);
            transform: translateY(0);
        }

        .login-card:hover {
            transform: translateY(-5px);
        }

        .login-header {
            background-color: var(--primary-color);
            color: white;
            padding: 30px 20px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('{% static "assets/img/pattern.png" %}');
            opacity: 0.1;
            z-index: 0;
        }

        .hospital-logo {
            position: relative;
            z-index: 1;
        }

        .hospital-logo img {
            max-width: 80px;
            margin-bottom: 15px;
            filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.2));
        }

        .login-header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            position: relative;
            z-index: 1;
        }

        .login-header p {
            margin: 10px 0 0;
            opacity: 0.9;
            font-size: 16px;
            position: relative;
            z-index: 1;
        }

        .login-body {
            padding: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: var(--dark-color);
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            font-size: 16px;
            border: 1px solid var(--border-color);
            border-radius: var(--radius);
            box-sizing: border-box;
            background-color: var(--light-color);
            transition: var(--transition);
        }

        .form-control:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.25);
            background-color: white;
        }

        .btn-login {
            width: 100%;
            background-color: var(--primary-color);
            color: white;
            border: none;
            padding: 12px;
            font-size: 16px;
            font-weight: 600;
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .btn-login:hover {
            background-color: var(--secondary-color);
            transform: translateY(-2px);
            box-shadow: var(--shadow);
        }

        .home-link {
            display: block;
            text-align: center;
            margin-top: 20px;
            color: #0d6efd;
            text-decoration: none;
        }

        .home-link:hover {
            text-decoration: underline;
        }

        .alert {
            padding: 12px 15px;
            margin-bottom: 20px;
            border-radius: 5px;
            font-size: 14px;
        }

        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeeba;
        }

        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-dismissible {
            position: relative;
            padding-right: 40px;
        }

        .close {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 20px;
            font-weight: bold;
            color: inherit;
            opacity: 0.5;
            background: none;
            border: none;
            cursor: pointer;
        }

        .close:hover {
            opacity: 1;
        }

        .hospital-logo {
            text-align: center;
            margin-bottom: 20px;
        }

        .hospital-logo img {
            max-width: 80px;
            height: auto;
        }

        .navbar-toggler {
            display: none;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 8px 12px;
            border-radius: var(--radius);
            transition: var(--transition);
        }

        .navbar-toggler:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        @media (max-width: 992px) {
            .page-container {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                padding: 10px 0;
            }

            .navbar-brand {
                padding: 10px 20px;
                justify-content: space-between;
                margin-bottom: 0;
            }

            .navbar-toggler {
                display: block;
            }

            .collapse:not(.show) {
                display: none;
            }

            .main-content {
                padding: 20px;
            }

            .login-card {
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="page-container">
        <!-- Sidebar Navigation -->
        <div class="sidebar">
            <div class="navbar-brand">
                <div class="brand-logo">
                    <img src="{% static 'assets/img/logo.png' %}" alt="HMS Logo" width="40" height="40">
                    <h2>HMS</h2>
                </div>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarmain">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <div class="collapse show" id="navbarmain">
                 <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'index' %}"><i class="fas fa-home"></i> Home</a>
                    </li>

                    <!-- Doctor Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="doctorDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-md"></i> Doctor
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="doctorDropdown">
                            <li><a class="dropdown-item" href="{% url 'login' %}"><i class="fas fa-sign-in-alt"></i> Doctor Login</a></li>
                            <li><a class="dropdown-item" href="{% url 'docsignup' %}"><i class="fas fa-user-plus"></i> Doctor Signup</a></li>
                        </ul>
                    </li>

                    <!-- Patient Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="patientDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-injured"></i> Patient
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="patientDropdown">
                            <li><a class="dropdown-item" href="{% url 'patreg' %}"><i class="fas fa-user-plus"></i> Patient Registration</a></li>
                            <li><a class="dropdown-item" href="{% url 'patient_meal_dashboard' %}"><i class="fas fa-utensils"></i> Patient Meal View</a></li>
                        </ul>
                    </li>

                    <!-- Kitchen Dropdown -->
                     <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle kitchen-link" href="#" id="kitchenDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                           <i class="fas fa-concierge-bell"></i> Kitchen
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="kitchenDropdown">
                            <li>
                                <!-- This button toggles the kitchen login form visibility -->
                                <button type="button" class="dropdown-item kitchen-link" onclick="document.getElementById('kitchen-login-form').style.display='block'; document.getElementById('regular-login-form').style.display='none'; return false;">
                                    <i class="fas fa-sign-in-alt"></i> Staff Login
                                </button>
                            </li>
                             <li><a class="dropdown-item" href="{% url 'kitchen_orders' %}"><i class="fas fa-receipt"></i> Orders</a></li>
                             <li><a class="dropdown-item" href="{% url 'kitchen_meals' %}"><i class="fas fa-utensils"></i> Meals</a></li>
                             <li><a class="dropdown-item" href="{% url 'kitchen_inventory' %}"><i class="fas fa-boxes"></i> Inventory</a></li>
                             <li><a class="dropdown-item" href="{% url 'kitchen_reports' %}"><i class="fas fa-chart-bar"></i> Reports</a></li>
                             {# Add other relevant kitchen links here if needed #}
                        </ul>
                    </li>
                     <li class="nav-item active">
                        {# Admin Login Link - Kept separate as it's a primary login type #}
                        <a class="nav-link" href="{% url 'login' %}"><i class="fas fa-user-shield"></i> Admin Login</a>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <div class="login-card">
                <div class="login-header">
                    <div class="hospital-logo">
                        <img src="{% static 'assets/img/logo-white1.png' %}" alt="Hospital Logo">
                    </div>
                    <h1>Hospital Management System</h1>
                    <p>Please sign in to access your account</p>
                </div>

                <div class="login-body">
                    {% if messages %}
                        {% for message in messages %}
                            {% if message.tags == 'error' %}
                                <div class="alert alert-warning alert-dismissible">
                                    <i class="fas fa-exclamation-triangle" style="margin-right: 8px;"></i> {{message}}
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}

                    {% if messages %}
                        {% for message in messages %}
                            {% if message.tags == 'success' %}
                                <div class="alert alert-success alert-dismissible">
                                    <i class="fas fa-check-circle" style="margin-right: 8px;"></i> {{message}}
                                    <button type="button" class="close" data-dismiss="alert">&times;</button>
                                </div>
                            {% endif %}
                        {% endfor %}
                    {% endif %}

                    <form id="regular-login-form" action="{% url 'doLogin' %}" method="POST">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="email">Email Address</label>
                            <input type="email" class="form-control" id="email" placeholder="Enter your email" name="email" required>
                        </div>

                        <div class="form-group">
                            <label for="password">Password</label>
                            <input type="password" class="form-control" id="password" placeholder="Enter your password" name="password" required>
                        </div>

                        <div class="form-group">
                            <button class="btn-login" type="submit">
                                <i class="fas fa-sign-in-alt" style="margin-right: 8px;"></i> Sign In
                            </button>
                        </div>
                    </form>

                    <form id="kitchen-login-form" action="{% url 'doLogin' %}" method="POST" style="display: none;">
                        {% csrf_token %}
                        <h4 class="text-center mb-4">Kitchen Staff Login</h4>
                        <p class="text-center mb-3"><small><a href="#" onclick="document.getElementById('kitchen-login-form').style.display='none'; document.getElementById('regular-login-form').style.display='block'; return false;">Switch to regular login</a></small></p>

                        <div class="form-group">
                            <label for="kitchen-email">Email Address</label>
                            <input type="email" class="form-control" id="kitchen-email" placeholder="Enter kitchen staff email" name="email" value="<EMAIL>" required>
                        </div>

                        <div class="form-group">
                            <label for="kitchen-password">Password</label>
                            <input type="password" class="form-control" id="kitchen-password" placeholder="Enter password" name="password" value="Kitchen@123" required>
                        </div>

                        <div class="form-group">
                            <button class="btn-login" type="submit">
                                <i class="fas fa-sign-in-alt" style="margin-right: 8px;"></i> Sign In
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <!-- Bootstrap 5 Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js" integrity="sha384-ka7Sk0Gln4gmtz2MlQnikT1wXgYsOg+OMhuP+IlRH9sENBO0LRn5q+8nbTov4+1p" crossorigin="anonymous"></script>
    <!-- jQuery (still needed for some custom scripts or older plugins if any) -->
    <script src="{% static 'assets/js/jquery-3.6.0.min.js'%}"></script>
    <script>
        // Simple script to make alert dismissible (using vanilla JS)
        document.addEventListener('DOMContentLoaded', function() {
            var closeButtons = document.querySelectorAll('.close');
            closeButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    var alert = this.parentElement;
                    alert.style.display = 'none';
                });
            });

            // Toggle navigation on mobile
            var navbarToggler = document.querySelector('.navbar-toggler');
            if (navbarToggler) {
                navbarToggler.addEventListener('click', function() {
                    var navbarMain = document.getElementById('navbarmain');
                    if (navbarMain.classList.contains('show')) {
                        navbarMain.classList.remove('show');
                    } else {
                        navbarMain.classList.add('show');
                    }
                });
            }
        });

        // Function to handle navigation
        function navigateTo(url) {
            window.location.href = url;
        }
    </script>
</body>
</html>
