<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print QR Code - {{ delivery.meal.name }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f8f9fa;
        }
        
        .print-container {
            width: 100%;
            max-width: 400px;
            margin: 20px auto;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .print-header {
            background-color: #0d6efd;
            color: white;
            padding: 15px;
            text-align: center;
        }
        
        .print-header h1 {
            margin: 0;
            font-size: 20px;
        }
        
        .print-body {
            padding: 20px;
            text-align: center;
        }
        
        .qr-code-container {
            margin: 20px auto;
            max-width: 200px;
        }
        
        .qr-code-container img {
            width: 100%;
            height: auto;
        }
        
        .meal-info {
            margin-top: 20px;
            text-align: left;
        }
        
        .meal-info h2 {
            margin: 0 0 10px 0;
            font-size: 18px;
            color: #333;
        }
        
        .meal-info p {
            margin: 5px 0;
            font-size: 14px;
            color: #666;
        }
        
        .meal-info .label {
            font-weight: bold;
            color: #333;
        }
        
        .print-footer {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #ddd;
        }
        
        .print-footer p {
            margin: 0;
            font-size: 12px;
            color: #666;
        }
        
        .print-button {
            display: block;
            margin: 20px auto;
            padding: 10px 20px;
            background-color: #0d6efd;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .print-button:hover {
            background-color: #0b5ed7;
        }
        
        @media print {
            .no-print {
                display: none;
            }
            
            body {
                background-color: white;
            }
            
            .print-container {
                box-shadow: none;
                border: none;
                margin: 0 auto;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-header">
            <h1>Hospital Meal Tracking</h1>
        </div>
        <div class="print-body">
            <div class="qr-code-container">
                <img src="{{ qr_code_image }}" alt="QR Code">
            </div>
            <div class="meal-info">
                <h2>{{ delivery.meal.name }}</h2>
                <p><span class="label">Meal Type:</span> {{ delivery.meal.get_meal_type_display }}</p>
                <p><span class="label">Patient:</span> {{ delivery.patient.name }}</p>
                <p><span class="label">Room:</span> {{ delivery.patient.room_no }}</p>
                <p><span class="label">Scheduled:</span> {{ delivery.scheduled_time|date:"M d, Y h:i A" }}</p>
                <p><span class="label">QR Code:</span> {{ delivery.qr_code }}</p>
            </div>
        </div>
        <div class="print-footer">
            <p>Scan this QR code to update meal status</p>
        </div>
    </div>
    
    <button class="print-button no-print" onclick="window.print()">
        <i class="fas fa-print"></i> Print QR Code
    </button>
    
    <div class="text-center no-print">
        <a href="{% url 'meal_delivery_detail' delivery.id %}" style="display: block; margin: 20px auto; text-align: center; color: #0d6efd; text-decoration: none;">
            Back to Meal Details
        </a>
    </div>
</body>
</html>
