-- <PERSON><PERSON> (Hospital Management System with Kitchen) SQL File
-- This file contains all the necessary tables and initial data for the hospital management system with kitchen module

-- Drop existing tables if they exist
DROP TABLE IF EXISTS django_session;
DROP TABLE IF EXISTS django_admin_log;
DROP TABLE IF EXISTS dasapp_mealstatuslog;
DROP TABLE IF EXISTS dasapp_mealdelivery;
DROP TABLE IF EXISTS dasapp_meal;
DROP TABLE IF EXISTS dasapp_mealplan;
DROP TABLE IF EXISTS dasapp_medicalhistory;
DROP TABLE IF EXISTS dasapp_appointment;
DROP TABLE IF EXISTS dasapp_addpatient;
DROP TABLE IF EXISTS dasapp_doctorreg;
DROP TABLE IF EXISTS dasapp_patientreg;
DROP TABLE IF EXISTS dasapp_customuser_user_permissions;
DROP TABLE IF EXISTS dasapp_customuser_groups;
DROP TABLE IF EXISTS dasapp_page;
DROP TABLE IF EXISTS dasapp_specialization;
DROP TABLE IF EXISTS dasapp_customuser;
DROP TABLE IF EXISTS auth_group_permissions;
DROP TABLE IF EXISTS auth_permission;
DROP TABLE IF EXISTS auth_group;
DROP TABLE IF EXISTS django_content_type;
DROP TABLE IF EXISTS django_migrations;

-- Create tables

-- Django migrations
CREATE TABLE django_migrations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    applied DATETIME NOT NULL
);

-- Django content types
CREATE TABLE django_content_type (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    app_label VARCHAR(100) NOT NULL,
    model VARCHAR(100) NOT NULL,
    UNIQUE (app_label, model)
);

-- Auth group
CREATE TABLE auth_group (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(150) NOT NULL UNIQUE
);

-- Auth permissions
CREATE TABLE auth_permission (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content_type_id INTEGER NOT NULL REFERENCES django_content_type (id),
    codename VARCHAR(100) NOT NULL,
    name VARCHAR(255) NOT NULL,
    UNIQUE (content_type_id, codename)
);

-- Auth group permissions
CREATE TABLE auth_group_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    group_id INTEGER NOT NULL REFERENCES auth_group (id),
    permission_id INTEGER NOT NULL REFERENCES auth_permission (id),
    UNIQUE (group_id, permission_id)
);

-- Custom user
CREATE TABLE dasapp_customuser (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    password VARCHAR(128) NOT NULL,
    last_login DATETIME NULL,
    is_superuser BOOLEAN NOT NULL,
    username VARCHAR(150) NOT NULL UNIQUE,
    first_name VARCHAR(150) NOT NULL,
    last_name VARCHAR(150) NOT NULL,
    email VARCHAR(254) NOT NULL,
    is_staff BOOLEAN NOT NULL,
    is_active BOOLEAN NOT NULL,
    date_joined DATETIME NOT NULL,
    user_type VARCHAR(50) NOT NULL,
    profile_pic VARCHAR(100) NOT NULL
);

-- Specialization
CREATE TABLE dasapp_specialization (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    sname VARCHAR(200) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- Page
CREATE TABLE dasapp_page (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    pagetitle VARCHAR(250) NOT NULL,
    address VARCHAR(250) NOT NULL,
    aboutus TEXT NOT NULL,
    email VARCHAR(200) NOT NULL,
    mobilenumber INTEGER NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL
);

-- Custom user groups
CREATE TABLE dasapp_customuser_groups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customuser_id INTEGER NOT NULL REFERENCES dasapp_customuser (id),
    group_id INTEGER NOT NULL REFERENCES auth_group (id),
    UNIQUE (customuser_id, group_id)
);

-- Custom user permissions
CREATE TABLE dasapp_customuser_user_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customuser_id INTEGER NOT NULL REFERENCES dasapp_customuser (id),
    permission_id INTEGER NOT NULL REFERENCES auth_permission (id),
    UNIQUE (customuser_id, permission_id)
);

-- Patient registration
CREATE TABLE dasapp_patientreg (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mobilenumber VARCHAR(11) NOT NULL UNIQUE,
    gender VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    regdate_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    admin_id INTEGER NULL REFERENCES dasapp_customuser (id) UNIQUE
);

-- Doctor registration
CREATE TABLE dasapp_doctorreg (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mobilenumber VARCHAR(11) NOT NULL,
    regdate_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    admin_id INTEGER NULL REFERENCES dasapp_customuser (id) UNIQUE,
    specialization_id_id INTEGER NOT NULL REFERENCES dasapp_specialization (id),
    fee DECIMAL(10, 5) NOT NULL
);

-- Add patient
CREATE TABLE dasapp_addpatient (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(250) NOT NULL,
    mobilenumber VARCHAR(11) NOT NULL UNIQUE,
    email VARCHAR(200) NOT NULL,
    gender VARCHAR(100) NOT NULL,
    address TEXT NOT NULL,
    age INTEGER NOT NULL,
    medicalhistory TEXT NOT NULL,
    regdate_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    doctor_id_id INTEGER NOT NULL REFERENCES dasapp_doctorreg (id),
    room_no VARCHAR(20) NULL
);

-- Appointment
CREATE TABLE dasapp_appointment (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    appointmentnumber INTEGER NOT NULL,
    date_of_appointment VARCHAR(250) NOT NULL,
    time_of_appointment VARCHAR(250) NOT NULL,
    additional_msg TEXT NOT NULL,
    remark VARCHAR(250) NOT NULL,
    status VARCHAR(200) NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    doctor_id_id INTEGER NOT NULL REFERENCES dasapp_doctorreg (id),
    pat_id_id INTEGER NOT NULL REFERENCES dasapp_patientreg (id),
    spec_id_id INTEGER NOT NULL REFERENCES dasapp_specialization (id)
);

-- Medical history
CREATE TABLE dasapp_medicalhistory (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    bloodpressure VARCHAR(250) NOT NULL,
    weight VARCHAR(250) NOT NULL,
    bloodsugar VARCHAR(250) NOT NULL,
    bodytemp VARCHAR(250) NOT NULL,
    prescription TEXT NOT NULL,
    visitingdate_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    pat_id_id INTEGER NOT NULL REFERENCES dasapp_addpatient (id)
);

-- Meal plan
CREATE TABLE dasapp_mealplan (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    dietary_restrictions VARCHAR(50) NOT NULL,
    special_instructions TEXT NULL,
    calories_per_day INTEGER NOT NULL,
    protein_per_day INTEGER NOT NULL,
    carbs_per_day INTEGER NOT NULL,
    fat_per_day INTEGER NOT NULL,
    is_active BOOLEAN NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    created_by_id INTEGER NULL REFERENCES dasapp_customuser (id),
    patient_id INTEGER NOT NULL REFERENCES dasapp_addpatient (id),
    updated_by_id INTEGER NULL REFERENCES dasapp_customuser (id)
);

-- Meal
CREATE TABLE dasapp_meal (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    meal_type VARCHAR(20) NOT NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    calories INTEGER NOT NULL,
    protein INTEGER NOT NULL,
    carbs INTEGER NOT NULL,
    fat INTEGER NOT NULL,
    preparation_instructions TEXT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    meal_plan_id INTEGER NOT NULL REFERENCES dasapp_mealplan (id)
);

-- Meal delivery
CREATE TABLE dasapp_mealdelivery (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    qr_code VARCHAR(255) NOT NULL UNIQUE,
    status VARCHAR(20) NOT NULL,
    scheduled_time DATETIME NOT NULL,
    preparation_time DATETIME NULL,
    delivery_time DATETIME NULL,
    consumption_time DATETIME NULL,
    return_time DATETIME NULL,
    feedback TEXT NULL,
    notes TEXT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    collected_by_id INTEGER NULL REFERENCES dasapp_customuser (id),
    delivered_by_id INTEGER NULL REFERENCES dasapp_customuser (id),
    meal_id INTEGER NOT NULL REFERENCES dasapp_meal (id),
    patient_id INTEGER NOT NULL REFERENCES dasapp_addpatient (id),
    prepared_by_id INTEGER NULL REFERENCES dasapp_customuser (id)
);

-- Meal status log
CREATE TABLE dasapp_mealstatuslog (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    previous_status VARCHAR(20) NOT NULL,
    new_status VARCHAR(20) NOT NULL,
    notes TEXT NULL,
    created_at DATETIME NOT NULL,
    changed_by_id INTEGER NULL REFERENCES dasapp_customuser (id),
    meal_delivery_id INTEGER NOT NULL REFERENCES dasapp_mealdelivery (id)
);

-- Django admin log
CREATE TABLE django_admin_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    object_id TEXT NULL,
    object_repr VARCHAR(200) NOT NULL,
    action_flag SMALLINT UNSIGNED NOT NULL,
    change_message TEXT NOT NULL,
    content_type_id INTEGER NULL REFERENCES django_content_type (id),
    user_id INTEGER NOT NULL REFERENCES dasapp_customuser (id),
    action_time DATETIME NOT NULL
);

-- Django session
CREATE TABLE django_session (
    session_key VARCHAR(40) PRIMARY KEY,
    session_data TEXT NOT NULL,
    expire_date DATETIME NOT NULL
);

-- Insert initial data

-- Insert content types
INSERT INTO django_content_type (id, app_label, model) VALUES
(1, 'admin', 'logentry'),
(2, 'auth', 'permission'),
(3, 'auth', 'group'),
(4, 'contenttypes', 'contenttype'),
(5, 'sessions', 'session'),
(6, 'dasapp', 'customuser'),
(7, 'dasapp', 'specialization'),
(8, 'dasapp', 'doctorreg'),
(9, 'dasapp', 'patientreg'),
(10, 'dasapp', 'addpatient'),
(11, 'dasapp', 'appointment'),
(12, 'dasapp', 'medicalhistory'),
(13, 'dasapp', 'page'),
(14, 'dasapp', 'mealplan'),
(15, 'dasapp', 'meal'),
(16, 'dasapp', 'mealdelivery'),
(17, 'dasapp', 'mealstatuslog');

-- Insert permissions (partial list for brevity)
INSERT INTO auth_permission (id, content_type_id, codename, name) VALUES
(1, 1, 'add_logentry', 'Can add log entry'),
(2, 1, 'change_logentry', 'Can change log entry'),
(3, 1, 'delete_logentry', 'Can delete log entry'),
(4, 1, 'view_logentry', 'Can view log entry'),
(5, 2, 'add_permission', 'Can add permission'),
(6, 2, 'change_permission', 'Can change permission'),
(7, 2, 'delete_permission', 'Can delete permission'),
(8, 2, 'view_permission', 'Can view permission'),
(9, 3, 'add_group', 'Can add group'),
(10, 3, 'change_group', 'Can change group'),
(11, 3, 'delete_group', 'Can delete group'),
(12, 3, 'view_group', 'Can view group'),
(13, 4, 'add_contenttype', 'Can add content type'),
(14, 4, 'change_contenttype', 'Can change content type'),
(15, 4, 'delete_contenttype', 'Can delete content type'),
(16, 4, 'view_contenttype', 'Can view content type'),
(17, 5, 'add_session', 'Can add session'),
(18, 5, 'change_session', 'Can change session'),
(19, 5, 'delete_session', 'Can delete session'),
(20, 5, 'view_session', 'Can view session'),
(21, 6, 'add_customuser', 'Can add custom user'),
(22, 6, 'change_customuser', 'Can change custom user'),
(23, 6, 'delete_customuser', 'Can delete custom user'),
(24, 6, 'view_customuser', 'Can view custom user');

-- Insert users (admin, doctor, patient, kitchen)
INSERT INTO dasapp_customuser (id, password, last_login, is_superuser, username, first_name, last_name, email, is_staff, is_active, date_joined, user_type, profile_pic) VALUES
(1, 'pbkdf2_sha256$600000$XtpDIXnHMEXvxhKlrn1Ygq$Ht8YGwWAFdA+zPDJD7hJEZPeULONfl9QOsLy6WfuYwQ=', '2023-05-18 10:00:00', 1, 'admin', 'Admin', 'User', '<EMAIL>', 1, 1, '2023-05-18 10:00:00', '1', 'media/profile_pic/default.jpg'),
(2, 'pbkdf2_sha256$600000$XtpDIXnHMEXvxhKlrn1Ygq$Ht8YGwWAFdA+zPDJD7hJEZPeULONfl9QOsLy6WfuYwQ=', '2023-05-18 10:00:00', 0, 'doctor', 'John', 'Smith', '<EMAIL>', 0, 1, '2023-05-18 10:00:00', '2', 'media/profile_pic/default.jpg'),
(3, 'pbkdf2_sha256$600000$XtpDIXnHMEXvxhKlrn1Ygq$Ht8YGwWAFdA+zPDJD7hJEZPeULONfl9QOsLy6WfuYwQ=', '2023-05-18 10:00:00', 0, 'patient', 'Jane', 'Doe', '<EMAIL>', 0, 1, '2023-05-18 10:00:00', '3', 'media/profile_pic/default.jpg'),
(4, 'pbkdf2_sha256$600000$XtpDIXnHMEXvxhKlrn1Ygq$Ht8YGwWAFdA+zPDJD7hJEZPeULONfl9QOsLy6WfuYwQ=', '2023-05-18 10:00:00', 0, 'kitchen', 'Kitchen', 'Staff', '<EMAIL>', 0, 1, '2023-05-18 10:00:00', '4', 'media/profile_pic/default.jpg');

-- Insert specializations
INSERT INTO dasapp_specialization (id, sname, created_at, updated_at) VALUES
(1, 'Cardiology', '2023-05-18 10:00:00', '2023-05-18 10:00:00'),
(2, 'Neurology', '2023-05-18 10:00:00', '2023-05-18 10:00:00'),
(3, 'Orthopedics', '2023-05-18 10:00:00', '2023-05-18 10:00:00'),
(4, 'Pediatrics', '2023-05-18 10:00:00', '2023-05-18 10:00:00'),
(5, 'Dermatology', '2023-05-18 10:00:00', '2023-05-18 10:00:00');

-- Insert doctor registration
INSERT INTO dasapp_doctorreg (id, mobilenumber, regdate_at, updated_at, admin_id, specialization_id_id, fee) VALUES
(1, '**********', '2023-05-18 10:00:00', '2023-05-18 10:00:00', 2, 1, 500.00);

-- Insert patient registration
INSERT INTO dasapp_patientreg (id, mobilenumber, gender, address, regdate_at, updated_at, admin_id) VALUES
(1, '**********', 'Female', '123 Main St, Anytown', '2023-05-18 10:00:00', '2023-05-18 10:00:00', 3);

-- Insert add patient
INSERT INTO dasapp_addpatient (id, name, mobilenumber, email, gender, address, age, medicalhistory, regdate_at, updated_at, doctor_id_id, room_no) VALUES
(1, 'Jane Doe', '**********', '<EMAIL>', 'Female', '123 Main St, Anytown', 35, 'No significant medical history', '2023-05-18 10:00:00', '2023-05-18 10:00:00', 1, '101');

-- Insert page information
INSERT INTO dasapp_page (id, pagetitle, address, aboutus, email, mobilenumber, created_at, updated_at) VALUES
(1, 'Hospital Management System', '456 Hospital Ave, Medical City', 'We are a state-of-the-art hospital providing comprehensive healthcare services.', '<EMAIL>', **********, '2023-05-18 10:00:00', '2023-05-18 10:00:00');

-- Insert meal plan
INSERT INTO dasapp_mealplan (id, dietary_restrictions, special_instructions, calories_per_day, protein_per_day, carbs_per_day, fat_per_day, is_active, created_at, updated_at, created_by_id, patient_id, updated_by_id) VALUES
(1, 'none', 'No special instructions', 2000, 100, 250, 70, 1, '2023-05-18 10:00:00', '2023-05-18 10:00:00', 2, 1, 2);

-- Insert meal
INSERT INTO dasapp_meal (id, meal_type, name, description, calories, protein, carbs, fat, preparation_instructions, created_at, updated_at, meal_plan_id) VALUES
(1, 'breakfast', 'Oatmeal with Fruit', 'Hearty oatmeal with fresh seasonal fruits', 350, 15, 60, 5, 'Cook oatmeal according to package instructions. Top with fresh fruits.', '2023-05-18 10:00:00', '2023-05-18 10:00:00', 1),
(2, 'lunch', 'Grilled Chicken Salad', 'Grilled chicken breast with mixed greens and light dressing', 450, 35, 20, 25, 'Grill chicken until fully cooked. Serve over mixed greens with dressing on the side.', '2023-05-18 10:00:00', '2023-05-18 10:00:00', 1),
(3, 'dinner', 'Baked Salmon with Vegetables', 'Baked salmon fillet with steamed vegetables and brown rice', 550, 40, 45, 25, 'Bake salmon at 375°F for 15-20 minutes. Serve with steamed vegetables and brown rice.', '2023-05-18 10:00:00', '2023-05-18 10:00:00', 1);

-- Insert meal delivery
INSERT INTO dasapp_mealdelivery (id, qr_code, status, scheduled_time, preparation_time, delivery_time, consumption_time, return_time, feedback, notes, created_at, updated_at, collected_by_id, delivered_by_id, meal_id, patient_id, prepared_by_id) VALUES
(1, 'MEAL-12345-ABCDE', 'preparing', '2023-05-18 08:00:00', NULL, NULL, NULL, NULL, NULL, 'Patient requested extra fruit', '2023-05-18 07:00:00', '2023-05-18 07:00:00', NULL, NULL, 1, 1, 4);

-- Insert meal status log
INSERT INTO dasapp_mealstatuslog (id, previous_status, new_status, notes, created_at, changed_by_id, meal_delivery_id) VALUES
(1, '', 'preparing', 'Meal order received', '2023-05-18 07:00:00', 2, 1);
