{% extends 'base.html' %}
{% block content %}




<div class="content container-fluid">

    <div class="page-header">
    <div class="row align-items-center">
    <div class="col">
    <h3 class="page-title">Search Appointments</h3>
    <ul class="breadcrumb">
    <li class="breadcrumb-item"><a href="{% url 'doctor_home' %}">Dashboard</a></li>
    <li class="breadcrumb-item active">Search Aappointment</li>
    </ul>
    </div>
    
    </div>
    </div>
    
    <div class="row">
    <div class="col-sm-12">
        <div class="card-block">
            <h4 class="sub-title">Search Appointment </h4>
            <form  method="GET">
                
                {% csrf_token %}
                <div class="form-group row">
                    <label class="col-sm-4 col-form-label">Search(By Fullname/Appointment Number)</label>
                    <div class="col-sm-10">
                        <input type="text" id="query" name="query" class="form-control" required="">
                    </div>
                </div>
               
             
                <button type="submit" class="btn btn-primary btn-user btn-block">Search</button>    
                        </form>
                    
                        </div>
    <div class="card card-table">

    <div class="card-body">
        {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'error' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
           {% if messages %}
        {% for message in messages %}
         {% if message.tags == 'success' %}
       <div class="alert alert-warning alert-dismissible fade show" role="alert">
      {{message}}
     <button type="button" class="close" data-dismiss="alert" aria-label="Close">
     <span aria-hidden="true">&times;</span>
         </button>
          </div>
       {% endif %}
        {% endfor %}
       {% endif %}
    <div class="table-responsive">
    <table class="table table-hover table-center mb-0 datatable">
    <thead>
    <tr>
        {% if patient %}
    <th>Appointment Number</th>
    <th>Patient Name</th>
    <th>Date of Appointment</th>
    <th>Time of Appointment</th>
    <th>Creation Date</th>
    <th>Status</th>
    <th class="text-right">Action</th>
    </tr>
    </thead>
    <tbody>
        {% for i in patient %}
    <tr>

    <td>{{i.appointmentnumber}}</td>
    <td>{{i.fullname}}</td>
    <td>{{i.date_of_appointment}}</td>
    <td>{{i.time_of_appointment}}</td>
    <td>{{i.created_at}}</td>
    {% if i.status == '0' %}
                                                <td>Not Updated Yet</td>
                                                {% else %}
                                                <td>{{ i.status}}</td>{% endif %}
    <td class="text-right">
    <div class="actions">
    <a href="{% url 'patientappointmentdetails' i.id %}">
    <i class="btn btn-primary btn-block">View</i>
    </a>
  
    </div>
    </td>
    </tr> {% endfor %}
   
    </tbody>
    </table>
    {% else %}
    {% if query %}
        <p style="font-size: 20px;color: blue;text-align: center;">No records found for: "{{ query }}"</p>
    
    {% endif %}

    {% endif %}
    

    </div>
    </div>
    </div>
    </div>
    </div>
    </div>


{% endblock %}