# Generated by Django 5.2 on 2025-04-10 07:02

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dasapp', '0004_alter_customuser_user_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='Meal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('meal_type', models.CharField(choices=[('breakfast', 'Breakfast'), ('lunch', 'Lunch'), ('dinner', 'Dinner'), ('snack', 'Snack')], max_length=20)),
                ('name', models.CharField(max_length=255)),
                ('description', models.TextField(blank=True, null=True)),
                ('calories', models.IntegerField()),
                ('protein', models.IntegerField()),
                ('carbs', models.IntegerField()),
                ('fat', models.IntegerField()),
                ('preparation_instructions', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='MealDelivery',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qr_code', models.CharField(max_length=255, unique=True)),
                ('status', models.CharField(choices=[('preparing', 'Preparing in Kitchen'), ('ready', 'Ready for Delivery'), ('in_transit', 'In Transit'), ('delivered', 'Delivered to Patient'), ('consumed', 'Consumed'), ('returned', 'Returned to Kitchen'), ('disposed', 'Disposed')], default='preparing', max_length=20)),
                ('scheduled_time', models.DateTimeField()),
                ('preparation_time', models.DateTimeField(blank=True, null=True)),
                ('delivery_time', models.DateTimeField(blank=True, null=True)),
                ('consumption_time', models.DateTimeField(blank=True, null=True)),
                ('return_time', models.DateTimeField(blank=True, null=True)),
                ('feedback', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('collected_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='collected_meals', to=settings.AUTH_USER_MODEL)),
                ('delivered_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='delivered_meals', to=settings.AUTH_USER_MODEL)),
                ('meal', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deliveries', to='dasapp.meal')),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meal_deliveries', to='dasapp.addpatient')),
                ('prepared_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='prepared_meals', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MealPlan',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('dietary_restrictions', models.CharField(choices=[('none', 'No Restrictions'), ('vegetarian', 'Vegetarian'), ('vegan', 'Vegan'), ('gluten_free', 'Gluten Free'), ('dairy_free', 'Dairy Free'), ('nut_free', 'Nut Free'), ('low_sodium', 'Low Sodium'), ('diabetic', 'Diabetic'), ('low_fat', 'Low Fat'), ('kosher', 'Kosher'), ('halal', 'Halal')], default='none', max_length=50)),
                ('special_instructions', models.TextField(blank=True, null=True)),
                ('calories_per_day', models.IntegerField(default=2000)),
                ('protein_per_day', models.IntegerField(default=50)),
                ('carbs_per_day', models.IntegerField(default=250)),
                ('fat_per_day', models.IntegerField(default=70)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_meal_plans', to=settings.AUTH_USER_MODEL)),
                ('patient', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meal_plans', to='dasapp.addpatient')),
                ('updated_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_meal_plans', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='meal',
            name='meal_plan',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='meals', to='dasapp.mealplan'),
        ),
        migrations.CreateModel(
            name='MealStatusLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('previous_status', models.CharField(choices=[('preparing', 'Preparing in Kitchen'), ('ready', 'Ready for Delivery'), ('in_transit', 'In Transit'), ('delivered', 'Delivered to Patient'), ('consumed', 'Consumed'), ('returned', 'Returned to Kitchen'), ('disposed', 'Disposed')], max_length=20)),
                ('new_status', models.CharField(choices=[('preparing', 'Preparing in Kitchen'), ('ready', 'Ready for Delivery'), ('in_transit', 'In Transit'), ('delivered', 'Delivered to Patient'), ('consumed', 'Consumed'), ('returned', 'Returned to Kitchen'), ('disposed', 'Disposed')], max_length=20)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('changed_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
                ('meal_delivery', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='status_logs', to='dasapp.mealdelivery')),
            ],
        ),
    ]
