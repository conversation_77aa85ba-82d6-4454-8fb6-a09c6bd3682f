{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="content container-fluid">
    <div class="page-header">
        <div class="row">
            <div class="col-sm-12">
                <h3 class="page-title">Meal Tracking Dashboard</h3>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'admin_home' %}">Dashboard</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Meal Tracking</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <!-- Status Cards -->
    <div class="row">
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-one">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ preparing_count }}</h3>
                            <h6>Preparing</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-two">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ ready_count }}</h3>
                            <h6>Ready for Delivery</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-three">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-truck"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ in_transit_count }}</h3>
                            <h6>In Transit</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-sm-6 col-12">
            <div class="card bg-four">
                <div class="card-body">
                    <div class="db-widgets d-flex justify-content-between align-items-center">
                        <div class="db-icon">
                            <i class="fas fa-check-double"></i>
                        </div>
                        <div class="db-info">
                            <h3>{{ delivered_count }}</h3>
                            <h6>Delivered</h6>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Links -->
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'meal_tracking' %}" class="btn btn-primary btn-block">
                                <i class="fas fa-list-alt mr-2"></i> Meal Tracking
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'kitchen_dashboard' %}" class="btn btn-info btn-block">
                                <i class="fas fa-utensils mr-2"></i> Kitchen Dashboard
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'create_meal_delivery' %}" class="btn btn-success btn-block">
                                <i class="fas fa-plus-circle mr-2"></i> New Meal Delivery
                            </a>
                        </div>
                        <div class="col-md-3 col-sm-6">
                            <a href="{% url 'scan_qr_code' %}" class="btn btn-warning btn-block">
                                <i class="fas fa-qrcode mr-2"></i> Scan QR Code
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Today's Deliveries -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Today's Meal Deliveries</h5>
                </div>
                <div class="card-body">
                    {% if today_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in today_deliveries %}
                                <tr>
                                    <td>{{ delivery.meal.name }}</td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% elif delivery.status == 'in_transit' %}badge-primary
                                            {% elif delivery.status == 'delivered' %}badge-success
                                            {% elif delivery.status == 'consumed' %}badge-secondary
                                            {% elif delivery.status == 'returned' %}badge-danger
                                            {% elif delivery.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No meal deliveries scheduled for today.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Deliveries -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title">Recent Meal Deliveries</h5>
                </div>
                <div class="card-body">
                    {% if recent_deliveries %}
                    <div class="table-responsive">
                        <table class="table table-hover table-center mb-0">
                            <thead>
                                <tr>
                                    <th>Meal</th>
                                    <th>Patient</th>
                                    <th>Scheduled Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for delivery in recent_deliveries %}
                                <tr>
                                    <td>{{ delivery.meal.name }}</td>
                                    <td>{{ delivery.patient.name }}</td>
                                    <td>{{ delivery.scheduled_time|date:"M d, Y h:i A" }}</td>
                                    <td>
                                        <span class="badge 
                                            {% if delivery.status == 'preparing' %}badge-warning
                                            {% elif delivery.status == 'ready' %}badge-info
                                            {% elif delivery.status == 'in_transit' %}badge-primary
                                            {% elif delivery.status == 'delivered' %}badge-success
                                            {% elif delivery.status == 'consumed' %}badge-secondary
                                            {% elif delivery.status == 'returned' %}badge-danger
                                            {% elif delivery.status == 'disposed' %}badge-dark
                                            {% endif %}">
                                            {{ delivery.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <a href="{% url 'meal_delivery_detail' delivery.id %}" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="alert alert-info">No recent meal deliveries found.</div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
