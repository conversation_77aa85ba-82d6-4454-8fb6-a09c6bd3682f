version: '3.8'

services:
  # MySQL Database
  db:
    image: mysql:8.0
    container_name: hospital_mysql
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_NAME:-hospital_db}
      MYSQL_USER: ${DB_USER:-hospital_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-hospital_password}
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root_password}
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backups:/backups
      - ./mysql/init:/docker-entrypoint-initdb.d
    ports:
      - "3306:3306"
    networks:
      - hospital_network
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p${DB_ROOT_PASSWORD:-root_password}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: hospital_redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - hospital_network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Django Web Application
  web:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: hospital_web
    restart: unless-stopped
    environment:
      - DJANGO_SETTINGS_MODULE=docappsystem.settings_production
      - DATABASE_ENGINE=mysql
      - DB_NAME=${DB_NAME:-hospital_db}
      - DB_USER=${DB_USER:-hospital_user}
      - DB_PASSWORD=${DB_PASSWORD:-hospital_password}
      - DB_HOST=db
      - DB_PORT=3306
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password}@redis:6379/1
      - SECRET_KEY=${SECRET_KEY:-django-insecure-change-this-in-production}
      - DEBUG=${DEBUG:-False}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-localhost,127.0.0.1,0.0.0.0}
      - DJANGO_SUPERUSER_USERNAME=${DJANGO_SUPERUSER_USERNAME:-admin}
      - DJANGO_SUPERUSER_EMAIL=${DJANGO_SUPERUSER_EMAIL:-<EMAIL>}
      - DJANGO_SUPERUSER_PASSWORD=${DJANGO_SUPERUSER_PASSWORD:-admin123}
    volumes:
      - static_volume:/app/staticfiles
      - media_volume:/app/media
      - logs_volume:/app/logs
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - hospital_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Web Server
  nginx:
    image: nginx:alpine
    container_name: hospital_nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/proxy_params:/etc/nginx/proxy_params:ro
      - static_volume:/app/staticfiles:ro
      - media_volume:/app/media:ro
      - nginx_cache:/var/cache/nginx
      - nginx_logs:/var/log/nginx
    depends_on:
      - web
    networks:
      - hospital_network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  static_volume:
    driver: local
  media_volume:
    driver: local
  logs_volume:
    driver: local
  nginx_cache:
    driver: local
  nginx_logs:
    driver: local

networks:
  hospital_network:
    driver: bridge
