# Hospital Management System with Kitchen Module

This is a comprehensive hospital management system with an integrated kitchen management module for meal tracking and delivery. The system is fully containerized with Docker and includes nginx web server support for production deployment.

## 🚀 Quick Start with Docker

### Development
```bash
cp .env.development .env
docker-compose -f docker-compose.dev.yml up -d
```
Access at: http://localhost

### Production
```bash
cp .env.production .env
# Edit .env with your settings
docker-compose up -d
```

## ✨ Features

- **User Management**: Admin, Doctor, Patient, Kitchen Staff roles
- **Appointment Scheduling**: Complete appointment management system
- **Patient Records**: Comprehensive patient information management
- **Medical History**: Track patient medical history and treatments
- **Meal Planning**: Advanced meal planning and dietary management
- **Kitchen Management**: Restaurant-style kitchen operations
- **QR Code Tracking**: QR code-based meal delivery tracking
- **Docker Support**: Full containerization with nginx
- **Production Ready**: Optimized for production deployment

## Database Setup

The system uses SQLite as its database. To set up the database:

1. Make sure you have SQLite installed on your system
2. Navigate to the project directory
3. Run the following command to create and populate the database:

```bash
sqlite3 db.sqlite3 < hmsk.sql
```

This will create all the necessary tables and populate them with initial data.

## Default Users

The system comes with the following default users:

1. **Admin**
   - Username: admin
   - Email: <EMAIL>
   - Password: Admin@123

2. **Doctor**
   - Username: doctor
   - Email: <EMAIL>
   - Password: Admin@123

3. **Patient**
   - Username: patient
   - Email: <EMAIL>
   - Password: Admin@123

4. **Kitchen Staff**
   - Username: kitchen
   - Email: <EMAIL>
   - Password: Kitchen@123

## 🐳 Docker Deployment

### Prerequisites
- Docker Engine 20.10+
- Docker Compose 2.0+
- 2GB RAM minimum
- 10GB free disk space

### Quick Start
```bash
# Development
docker-compose -f docker-compose.dev.yml up -d

# Production with PostgreSQL
docker-compose up -d

# Production with MySQL
docker-compose -f docker-compose.mysql.yml up -d
```

### Environment Configuration
```bash
# Copy and edit environment file
cp .env.template .env
# Edit .env with your configuration
```

### Access the Application
- **Development**: http://localhost
- **Production**: http://your-domain.com
- **Admin Panel**: http://localhost/admin

For detailed deployment instructions, see [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md)

## 🔧 Traditional Installation

### Running the Application (Non-Docker)

To run the application traditionally, use the following command:

```bash
python C:\xampp\htdocs\hms\hospital\manage.py runserver
```

Then open your browser and navigate to http://127.0.0.1:8000/login

## Kitchen Management

The kitchen management module allows:

1. Kitchen staff to manage meal preparation and delivery
2. Doctors to order meals for patients
3. Patients to view their meal plans and provide feedback

## 🍽️ Kitchen Management Features

The kitchen management module provides restaurant-style operations:

1. **Meal Planning**: Create and manage meal plans for patients
2. **Inventory Management**: Track ingredients and supplies
3. **Order Management**: Process meal orders from doctors
4. **QR Code Tracking**: Track meal preparation and delivery
5. **Patient Dietary Requirements**: Manage special dietary needs
6. **Kitchen Staff Dashboard**: Comprehensive kitchen operations interface

## 📱 QR Code Tracking

The system uses QR codes to track meal delivery status:

1. Kitchen staff generate QR codes for each meal
2. Delivery staff scan QR codes to update delivery status
3. Patients can scan QR codes to provide feedback
4. Real-time tracking of meal preparation and delivery

## 🏗️ Architecture

### Docker Architecture
- **nginx**: Reverse proxy, static files, SSL termination
- **Django**: Hospital management application
- **PostgreSQL/MySQL**: Primary database
- **Redis**: Caching and session storage
- **Backup Service**: Automated database backups

### Security Features
- Rate limiting and DDoS protection
- Security headers (XSS, CSRF protection)
- SSL/TLS support
- Role-based access control
- Secure password policies

## 📊 Monitoring and Maintenance

### Health Checks
```bash
# Check all services
docker-compose ps

# View logs
docker-compose logs -f

# Health endpoint
curl http://localhost/health
```

### Backup and Restore
```bash
# Create backup
docker-compose exec backup /scripts/backup.sh

# Restore from backup
docker-compose exec backup /scripts/restore.sh /backups/backup_file.sql.gz
```

## 🔐 Default Credentials

### Development Environment
- **Admin**: admin / admin123
- **Doctor**: <EMAIL> / doctor123
- **Patient**: <EMAIL> / patient123
- **Kitchen**: <EMAIL> / kitchen123

### Production Environment
Configure your own credentials in `.env`:
```env
DJANGO_SUPERUSER_USERNAME=your_admin
DJANGO_SUPERUSER_EMAIL=<EMAIL>
DJANGO_SUPERUSER_PASSWORD=secure_password
```
